The weather plugin shall run every 12 hours
It will store the results in the Supabase Postgress database
The UI will allow the user to provide the RapidAPI API key.  if the key already exists in the database it will display "key already saved" and disable the box
if key does not exist, upon clicking save, the key will be validated via a call directly to the api endpoint.  a 200 response means key is valid.
if key is valid, a message shows key was validated
if key is not valid, message indicates such

plugin UI provides additional text entry boxes 
One is latitude, the one is longitude.  these are required params for the api.  both of these entry boxes should only allow digits

U<PERSON> also has a pulldown menu "Units" with two optoins "Metric" or "Imperial".  these are not params, but important

prior to storage of the response data, the data must be normalized to the units required.  by default the response contains metric units.  if the user chooses Imperial, the units must be changed prior to storage.

for example "temperatureMax": 29.5..  the 29.5 is in metric units and must be converted if the user has chosen imperial.  this is just one example

API call example

```javascript
const data = null; const xhr = new XMLHttpRequest(); xhr.withCredentials = true; xhr.addEventListener('readystatechange', function () { if (this.readyState === this.DONE) { console.log(this.responseText); } }); xhr.open('GET', 'https://easy-weather1.p.rapidapi.com/daily/5?latitude=1.28&longitude=103.86'); xhr.setRequestHeader('x-rapidapi-key', '**************************************************'); xhr.setRequestHeader('x-rapidapi-host', 'easy-weather1.p.rapidapi.com'); xhr.send(data);
```

API response example

```
{ "resource": "/daily/5", "parameters": { "latitude": "1.28", "longitude": "103.86" }, "forecastDaily": { "reportedTime": "2023-04-04T16:00:00Z", "readTime": "2023-04-04T17:13:14Z", "days": [ { "forecastStart": "2023-04-04T16:00:00Z", "forecastEnd": "2023-04-05T16:00:00Z", "conditionCode": "HeavyRain", "maxUvIndex": 7, "temperatureMax": 29.5, "temperatureMin": 25.84, "precipitationChance": 0.83, "precipitationType": "rain", "precipitationAmount": 11.6, "snowfallAmount": 0, "daytimeForecast": { "forecastStart": "2023-04-04T23:00:00Z", "forecastEnd": "2023-04-05T11:00:00Z", "cloudCover": 0.75, "conditionCode": "HeavyRain", "humidity": 0.8, "precipitationChance": 0.75, "precipitationType": "rain", "precipitationAmount": 10.5, "snowfallAmount": 0, "windDirection": 267, "windSpeed": 13.72 }, "overnightForecast": { "forecastStart": "2023-04-05T11:00:00Z", "forecastEnd": "2023-04-05T23:00:00Z", "cloudCover": 0.8, "conditionCode": "Drizzle", "humidity": 0.86, "precipitationChance": 0.39, "precipitationType": "rain", "precipitationAmount": 0.8, "snowfallAmount": 0, "windDirection": 299, "windSpeed": 9.97 } }, { "forecastStart": "2023-04-05T16:00:00Z", "forecastEnd": "2023-04-06T16:00:00Z", "conditionCode": "Rain", "maxUvIndex": 7, "temperatureMax": 30.07, "temperatureMin": 25.66, "precipitationChance": 0.69, "precipitationType": "rain", "precipitationAmount": 6.8, "snowfallAmount": 0, "daytimeForecast": { "forecastStart": "2023-04-05T23:00:00Z", "forecastEnd": "2023-04-06T11:00:00Z", "cloudCover": 0.82, "conditionCode": "Rain", "humidity": 0.77, "precipitationChance": 0.54, "precipitationType": "rain", "precipitationAmount": 6, "snowfallAmount": 0, "windDirection": 257, "windSpeed": 11.84 }, "overnightForecast": { "forecastStart": "2023-04-06T11:00:00Z", "forecastEnd": "2023-04-06T23:00:00Z", "cloudCover": 0.82, "conditionCode": "Drizzle", "humidity": 0.85, "precipitationChance": 0.31, "precipitationType": "rain", "precipitationAmount": 0.4, "snowfallAmount": 0, "windDirection": 321, "windSpeed": 6.97 } }, { "forecastStart": "2023-04-06T16:00:00Z", "forecastEnd": "2023-04-07T16:00:00Z", "conditionCode": "Rain", "maxUvIndex": 8, "temperatureMax": 30.03, "temperatureMin": 25.68, "precipitationChance": 0.64, "precipitationType": "rain", "precipitationAmount": 5.7, "snowfallAmount": 0, "daytimeForecast": { "forecastStart": "2023-04-06T23:00:00Z", "forecastEnd": "2023-04-07T11:00:00Z", "cloudCover": 0.75, "conditionCode": "Rain", "humidity": 0.77, "precipitationChance": 0.5, "precipitationType": "rain", "precipitationAmount": 4.5, "snowfallAmount": 0, "windDirection": 246, "windSpeed": 5.96 }, "overnightForecast": { "forecastStart": "2023-04-07T11:00:00Z", "forecastEnd": "2023-04-07T23:00:00Z", "cloudCover": 0.53, "conditionCode": "Rain", "humidity": 0.85, "precipitationChance": 0.44, "precipitationType": "rain", "precipitationAmount": 3.6, "snowfallAmount": 0, "windDirection": 3, "windSpeed": 6.94 } }, { "forecastStart": "2023-04-07T16:00:00Z", "forecastEnd": "2023-04-08T16:00:00Z", "conditionCode": "Rain", "maxUvIndex": 7, "temperatureMax": 30.08, "temperatureMin": 25.41, "precipitationChance": 0.7, "precipitationType": "rain", "precipitationAmount": 8.9, "snowfallAmount": 0, "daytimeForecast": { "forecastStart": "2023-04-07T23:00:00Z", "forecastEnd": "2023-04-08T11:00:00Z", "cloudCover": 0.73, "conditionCode": "Rain", "humidity": 0.78, "precipitationChance": 0.42, "precipitationType": "rain", "precipitationAmount": 1.6, "snowfallAmount": 0, "windDirection": 3, "windSpeed": 5.91 }, "overnightForecast": { "forecastStart": "2023-04-08T11:00:00Z", "forecastEnd": "2023-04-08T23:00:00Z", "cloudCover": 0.78, "conditionCode": "Rain", "humidity": 0.87, "precipitationChance": 0.56, "precipitationType": "rain", "precipitationAmount": 4.4, "snowfallAmount": 0, "windDirection": 4, "windSpeed": 6.76 } }, { "forecastStart": "2023-04-08T16:00:00Z", "forecastEnd": "2023-04-09T16:00:00Z", "conditionCode": "Drizzle", "maxUvIndex": 8, "temperatureMax": 30.54, "temperatureMin": 25.63, "precipitationChance": 0.55, "precipitationType": "rain", "precipitationAmount": 2.1, "snowfallAmount": 0, "daytimeForecast": { "forecastStart": "2023-04-08T23:00:00Z", "forecastEnd": "2023-04-09T11:00:00Z", "cloudCover": 0.68, "conditionCode": "Drizzle", "humidity": 0.76, "precipitationChance": 0.34, "precipitationType": "rain", "precipitationAmount": 2.1, "snowfallAmount": 0, "windDirection": 26, "windSpeed": 8.61 }, "overnightForecast": { "forecastStart": "2023-04-09T11:00:00Z", "forecastEnd": "2023-04-09T23:00:00Z", "cloudCover": 0.61, "conditionCode": "PartlyCloudy", "humidity": 0.84, "precipitationChance": 0, "precipitationType": "clear", "precipitationAmount": 0, "snowfallAmount": 0, "windDirection": 5, "windSpeed": 7.99 } } ] } }
```

Some explanation about the response:

 the response provides five day forecast. The index keys indicate the days so 0 is today (days.0, days.1, days.2 etc)
the following key's values shall be stored in the database:

day.conditionCode
day.humidity
day.precipitationChance
day.precipitationType
day.precipitationAmount
day.snowfallAmount
day.windSpeed
day.maxUvIndex

night.conditionCode
night.humidity
night.precipitationChance
night.precipitationType
night.precipitationAmount
night.snowfallAmount
night.windSpeed

