
Features:
Plugin UI contains text entry box
If RapidAPI key is not present in Supabase Postres (via secrets manager check), the text entry box will be active.  and a save button will be available for saving the api key.  
If the key is new, there will also be a backend api confirmation / verification test at the billboard endpoint
If the key is not new, it was already stored, then the text entry box will show placeholder text "Already stored, update only if required".

the plugin code will call a core function in the project to create the supporting table required for saving the data from billboard.  The columns: rank, artist, weeks at no.1, peak position, weeks on chart, detail, auto increment id.

the date param for the api call shall always be "today". meaning when a validated key exists and user has turned on the plugin, the system will fetch "today" charts.

the plugin will execute every 8 hours


```javascript
date=2019-05-11&
```

Example Request
```javascript
const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText);
	}
});

xhr.open('GET', 'https://billboard-api2.p.rapidapi.com/hot-100?date=2019-05-11&range=1-10');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
xhr.setRequestHeader('x-rapidapi-host', 'billboard-api2.p.rapidapi.com');

xhr.send(data);
```

Example response
```json
{ "info": { "category": "Billboard", "chart": "HOT 100", "date": "2019-05-11", "source": "Billboard-API" }, "content": { "1": { "rank": "1", "title": "Old Town Road", "artist": "Lil Nas X Featuring Billy Ray Cyrus", "weeks at no.1": "5", "last week": "1", "peak position": "1", "weeks on chart": "9", "detail": "same" }, "2": { "rank": "2", "title": "ME!", "artist": "Taylor Swift Featuring Brendon Urie", "last week": "100", "peak position": "2", "weeks on chart": "2", "detail": "up" }, "3": { "rank": "3", "title": "Wow.", "artist": "Post Malone", "last week": "2", "peak position": "2", "weeks on chart": "19", "detail": "down" }, "4": { "rank": "4", "title": "Sucker", "artist": "Jonas Brothers", "last week": "5", "peak position": "1", "weeks on chart": "9", "detail": "up" }, "5": { "rank": "5", "title": "Sunflower (Spider-Man: Into The Spider-Verse)", "artist": "Post Malone & Swae Lee", "last week": "3", "peak position": "1", "weeks on chart": "28", "detail": "down" }, "6": { "rank": "6", "title": "7 Rings", "artist": "Ariana Grande", "last week": "4", "peak position": "1", "weeks on chart": "15", "detail": "down" }, "7": { "rank": "7", "title": "Without Me", "artist": "Halsey", "last week": "6", "peak position": "1", "weeks on chart": "30", "detail": "down" }, "8": { "rank": "8", "title": "Dancing With A Stranger", "artist": "Sam Smith & Normani", "last week": "7", "peak position": "7", "weeks on chart": "16", "detail": "down" }, "9": { "rank": "9", "title": "Bad Guy", "artist": "Billie Eilish", "last week": "9", "peak position": "7", "weeks on chart": "5", "detail": "same" }, "10": { "rank": "10", "title": "Talk", "artist": "Khalid", "last week": "8", "peak position": "8", "weeks on chart": "12", "detail": "down" } } }
```


