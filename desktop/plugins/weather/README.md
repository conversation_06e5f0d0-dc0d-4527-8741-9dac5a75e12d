# Lifeboard Weather Plugin

A data provider plugin for Lifeboard that delivers weather information to other components through a clean API. This plugin handles all the complexity of weather data fetching, processing, and storage, exposing a simple interface for other components to consume.

## Features

- **Current Weather Data**: Get real-time weather conditions for any location
- **Forecast Data**: Access 5-day weather forecasts with hourly breakdowns
- **Weather Alerts**: Receive severe weather alerts for configured locations
- **Automatic Synchronization**: Data is automatically refreshed on a 12-hour schedule
- **Unit Conversion**: Supports both Metric and Imperial units
- **Secure API Key Management**: API keys are stored securely in Lifeboard's settings

## Installation

1. Copy the `weather` directory to your Lifeboard plugins directory
2. Ensure the database migration has been applied (see Database Setup)
3. Configure your weather API key and location in the plugin settings

## Database Setup

The plugin requires a database table for storing weather data. Apply the migration:

```sql
-- Run this in your Lifeboard database
\i db/migrations/04_weather_tables.sql
```

## Configuration

Configure the plugin by setting these environment variables or through the Lifeboard settings UI:

```env
WEATHER_API_KEY=your_api_key_here
WEATHER_LATITUDE=40.7128
WEATHER_LONGITUDE=-74.0060
WEATHER_UNITS=metric  # or 'imperial'
```

## API Reference

### Current Weather

Get the current weather conditions.

**Endpoint**: `GET /api/plugins/weather/current`

**Response**:
```json
{
  "location": {
    "name": "New York",
    "region": "New York",
    "country": "United States of America",
    "lat": 40.71,
    "lon": -74.01,
    "timezone": "America/New_York"
  },
  "current": {
    "last_updated": "2023-06-30T12:00:00-04:00",
    "temp_c": 25,
    "temp_f": 77,
    "condition": {
      "text": "Sunny",
      "icon": "//cdn.weatherapi.com/weather/64x64/day/113.png",
      "code": 1000
    },
    "wind_kph": 10,
    "wind_mph": 6.2,
    "wind_degree": 180,
    "wind_dir": "S",
    "pressure_mb": 1013,
    "pressure_in": 29.92,
    "precip_mm": 0,
    "precip_in": 0,
    "humidity": 60,
    "cloud": 0,
    "feelslike_c": 26,
    "feelslike_f": 78.8,
    "vis_km": 10,
    "vis_miles": 6,
    "uv": 7,
    "gust_kph": 16.6,
    "gust_mph": 10.3,
    "air_quality": {
      "co": 210.3,
      "no2": 2.8,
      "o3": 68.7,
      "so2": 1.2,
      "pm2_5": 2.9,
      "pm10": 4.5,
      "us-epa-index": 1,
      "gb-defra-index": 1
    }
  }
}
```

### Forecast

Get weather forecast for the next few days.

**Endpoint**: `GET /api/plugins/weather/forecast?days=5`

**Parameters**:
- `days` (optional): Number of days of forecast to return (1-10, default: 5)

**Response**:
```json
{
  "location": {
    "name": "New York",
    "region": "New York",
    "country": "United States of America",
    "lat": 40.71,
    "lon": -74.01,
    "timezone": "America/New_NewYork"
  },
  "current": {
    "last_updated": "2023-06-30T12:00:00-04:00",
    "temp_c": 25,
    "temp_f": 77,
    "condition": {
      "text": "Sunny",
      "icon": "//cdn.weatherapi.com/weather/64x64/day/113.png",
      "code": 1000
    }
  },
  "forecast": {
    "forecastday": [
      {
        "date": "2023-06-30",
        "date_epoch": 1688083200,
        "day": {
          "maxtemp_c": 28,
          "maxtemp_f": 82.4,
          "mintemp_c": 18,
          "mintemp_f": 64.4,
          "avgtemp_c": 23,
          "avgtemp_f": 73.4,
          "maxwind_mph": 9.3,
          "maxwind_kph": 15,
          "totalprecip_mm": 0,
          "totalprecip_in": 0,
          "totalsnow_cm": 0,
          "avgvis_km": 10,
          "avgvis_miles": 6,
          "avghumidity": 65,
          "daily_will_it_rain": 0,
          "daily_chance_of_rain": 0,
          "daily_will_it_snow": 0,
          "daily_chance_of_snow": 0,
          "condition": {
            "text": "Sunny",
            "icon": "//cdn.weatherapi.com/weather/64x64/day/113.png",
            "code": 1000
          },
          "uv": 8
        },
        "astro": {
          "sunrise": "05:30 AM",
          "sunset": "08:30 PM",
          "moonrise": "10:00 AM",
          "moonset": "11:00 PM",
          "moon_phase": "First Quarter",
          "moon_illumination": "50"
        },
        "hour": [
          {
            "time_epoch": 1688083200,
            "time": "2023-06-30 00:00",
            "temp_c": 18,
            "temp_f": 64.4,
            "is_day": 0,
            "condition": {
              "text": "Clear",
              "icon": "//cdn.weatherapi.com/weather/64x64/night/113.png",
              "code": 1000
            },
            "wind_kph": 6.8,
            "wind_mph": 4.3,
            "wind_degree": 180,
            "wind_dir": "S",
            "pressure_mb": 1013,
            "pressure_in": 29.92,
            "precip_mm": 0,
            "precip_in": 0,
            "humidity": 70,
            "cloud": 0,
            "feelslike_c": 18,
            "feelslike_f": 64.4,
            "windchill_c": 18,
            "windchill_f": 64.4,
            "heatindex_c": 18,
            "heatindex_f": 64.4,
            "dewpoint_c": 12.5,
            "dewpoint_f": 54.5,
            "will_it_rain": 0,
            "chance_of_rain": 0,
            "will_it_snow": 0,
            "chance_of_snow": 0,
            "vis_km": 10,
            "vis_miles": 6,
            "gust_kph": 10.4,
            "gust_mph": 6.5,
            "uv": 1
          }
        ]
      }
    ]
  }
}
```

### Weather Alerts

Get any active weather alerts for the configured location.

**Endpoint**: `GET /api/plugins/weather/alerts`

**Response**:
```json
{
  "alerts": [
    {
      "headline": "Heat Advisory",
      "msgtype": "Alert",
      "severity": "Moderate",
      "urgency": "Expected",
      "areas": "New York County (Manhattan); Kings County (Brooklyn); Queens County (Queens); Richmond County (Staten Island); Bronx County (The Bronx)",
      "category": "Met",
      "certainty": "Likely",
      "event": "Heat Advisory",
      "note": "A Heat Advisory means that a period of hot temperatures is expected. The combination of hot temperatures and high humidity will create a situation in which heat illnesses are possible. Drink plenty of fluids, stay in an air-conditioned room, stay out of the sun, and check up on relatives and neighbors.",
      "effective": "2023-07-01T12:00:00-04:00",
      "expires": "2023-07-01T20:00:00-04:00",
      "desc": "...detailed alert description...",
      "instruction": "...safety instructions..."
    }
  ]
}
```

### Manual Sync

Manually trigger a weather data synchronization.

**Endpoint**: `POST /api/plugins/weather/sync`

**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "message": "Weather data synced successfully",
  "lastSync": "2023-06-30T12:05:23.000Z",
  "nextSync": "2023-07-01T00:05:23.000Z"
}
```

### Plugin Status

Get the current status of the weather plugin.

**Endpoint**: `GET /api/plugins/weather/status`

**Response**:
```json
{
  "isInitialized": true,
  "lastSync": "2023-06-30T12:05:23.000Z",
  "nextSync": "2023-07-01T00:05:23.000Z",
  "syncStatus": "idle",
  "hasApiKey": true,
  "hasLocation": true
}
```

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages in the following format:

```json
{
  "error": {
    "code": "missing_api_key",
    "message": "API key is required",
    "details": "Please configure your weather API key in the plugin settings"
  }
}
```

## Usage Examples

### JavaScript

```javascript
// Get current weather
async function getCurrentWeather() {
  try {
    const response = await fetch('/api/plugins/weather/current');
    const data = await response.json();
    console.log('Current temperature:', data.current.temp_c, '°C');
  } catch (error) {
    console.error('Error fetching weather:', error);
  }
}

// Get 7-day forecast
async function getForecast() {
  try {
    const response = await fetch('/api/plugins/weather/forecast?days=7');
    const data = await response.json();
    console.log('Forecast for tomorrow:', data.forecast.forecastday[1].day);
  } catch (error) {
    console.error('Error fetching forecast:', error);
  }
}

// Manually sync weather data (requires authentication)
async function syncWeather() {
  try {
    const response = await fetch('/api/plugins/weather/sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_AUTH_TOKEN'
      }
    });
    const result = await response.json();
    console.log('Sync result:', result);
  } catch (error) {
    console.error('Error syncing weather:', error);
  }
}
```

## Development

### Running Tests

```bash
# Install test dependencies
npm install --no-package-lock --no-save mocha chai sinon nock node-fetch@2

# Run tests
./run-tests.sh
```

### Environment Variables

For development, create a `.env` file in the plugin directory:

```env
WEATHER_API_KEY=your_test_api_key
NODE_ENV=development
DEBUG=lifeboard:weather:*
```

## License

This plugin is part of the Lifeboard project and is licensed under the [MIT License](LICENSE).
