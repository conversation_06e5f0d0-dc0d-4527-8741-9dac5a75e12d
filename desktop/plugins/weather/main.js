/**
 * Weather Plugin
 * 
 * Main entry point for the Weather plugin.
 * Provides weather forecasting functionality with 5-day forecasts
 * and real-time weather data from a weather API.
 * 
 * Features:
 * - Secure API key management for weather data providers
 * - 12-hour scheduled weather data synchronization
 * - Support for both Metric and Imperial units
 * - Location-based weather forecasting
 * - Detailed 5-day forecast display
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Import core components
const path = require('path');

// Import plugin modules
const WeatherAPI = require('./src/weather-api');
const DataProcessor = require('./src/data-processor');
const SyncManager = require('./src/sync-manager');

/**
 * Main Weather Plugin Class
 * 
 * Orchestrates all plugin functionality and manages the integration
 * between weather data and Lifeboard's local processing system.
 * 
 * @class WeatherPlugin
 */
class WeatherPlugin {
  /**
   * Creates a new WeatherPlugin instance
   * 
   * @param {Object} api - The Plugin API instance
   */
  constructor(api) {
    this.api = api;
    this.logger = api.logger; // Use CoreLogger provided by PluginAPI
    this.weatherAPI = new WeatherAPI(api, this.logger);
    this.dataProcessor = new DataProcessor(api, this.logger);
    this.syncManager = new SyncManager(api, this.logger, this.weatherAPI, this.dataProcessor);
    this.isInitialized = false;
    this.ribbonIconId = null;
  }

  /**
   * Initializes the plugin
   * 
   * Sets up UI elements, registers commands, and starts sync if configured
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.logger.logLifecycle('weather-plugin-init-start');

      // Register all commands
      await this.registerCommands();

      // Setup UI elements
      await this.setupUI();

      // Initialize sync manager
      await this.syncManager.initialize();

      // Check if API key is configured and start initial sync if needed
      await this.checkInitialSetup();

      this.isInitialized = true;

      await this.logger.logLifecycle('weather-plugin-init-complete', {
        commands: 'registered',
        ui: 'setup',
        sync: 'initialized'
      });

      await this.logger.info('Weather plugin initialized successfully');
    } catch (error) {
      await this.logger.error('Failed to initialize Weather plugin', error);
      throw error;
    }
  }

  /**
   * Registers all plugin commands with the command palette
   * 
   * @returns {Promise<void>}
   */
  async registerCommands() {
    if (!this.api.commands || !this.api.commands.register) {
      await this.logger.warn('Command registration not available');
      return;
    }

    try {
      // Configuration Command
      this.api.commands.register('weather-configure', async () => {
        await this.showConfigurationModal();
      });

      this.api.commands.setMetadata('weather-configure', {
        category: 'Weather',
        description: 'Configure Weather API key and location settings',
        icon: 'cog',
        tags: ['weather', 'config', 'api-key'],
        hotkey: 'Cmd+Shift+W'
      });

      // Sync Command
      this.api.commands.register('weather-sync-now', async () => {
        await this.performManualSync();
      });

      this.api.commands.setMetadata('weather-sync-now', {
        category: 'Weather',
        description: 'Manually sync weather data',
        icon: 'cloud-download',
        tags: ['weather', 'sync', 'manual']
      });

      // Dashboard Command
      this.api.commands.register('weather-dashboard', async () => {
        await this.showWeatherDashboard();
      });

      this.api.commands.setMetadata('weather-dashboard', {
        category: 'Weather',
        description: 'Open Weather Dashboard',
        icon: 'cloud-sun',
        tags: ['weather', 'dashboard', 'forecast']
      });

      // View Data Command
      this.api.commands.register('weather-view-data', async () => {
        await this.showWeatherDataView();
      });

      this.api.commands.setMetadata('weather-view-data', {
        category: 'Weather',
        description: 'View raw weather data',
        icon: 'database',
        tags: ['weather', 'data', 'view']
      });

      // Validate API Key Command
      this.api.commands.register('weather-validate-key', async () => {
        await this.validateAPIKey();
      });

      this.api.commands.setMetadata('weather-validate-key', {
        category: 'Weather',
        description: 'Validate Weather API key',
        icon: 'key',
        tags: ['weather', 'api-key', 'validate']
      });

      await this.logger.info('Weather plugin commands registered successfully');
    } catch (error) {
      await this.logger.error('Failed to register commands', error);
    }
  }

  /**
   * Sets up UI elements including ribbon icons
   * 
   * @returns {Promise<void>}
   */
  async setupUI() {
    try {
      if (this.api.ui && this.api.ui.addRibbonIcon) {
        // Add ribbon icon for quick access to weather dashboard
        this.ribbonIconId = await this.api.ui.addRibbonIcon({
          icon: 'cloud-sun',
          title: 'Weather Dashboard',
          onClick: () => this.showWeatherDashboard()
        });
        
        await this.logger.debug('Weather plugin UI elements initialized');
      }
    } catch (error) {
      await this.logger.error('Failed to setup UI elements', error);
    }
  }

  /**
   * Checks initial setup and starts sync if configured
   * 
   * @returns {Promise<void>}
   */
  async checkInitialSetup() {
    try {
      const settings = await this.api.settings.get();
      
      if (settings && settings.apiKey && settings.latitude && settings.longitude) {
        // If we have all required settings, start sync
        await this.syncManager.startAutoSync();
      } else {
        await this.logger.info('Weather plugin not fully configured. Please set up API key and location.');
      }
    } catch (error) {
      await this.logger.error('Error during initial setup check', error);
    }
  }

  /**
   * Shows the configuration modal
   * 
   * @returns {Promise<void>}
   */
  async showConfigurationModal() {
    try {
      if (this.api.windows && this.api.windows.open) {
        // Use the new window system if available
        await this.api.windows.open({
          id: 'weather-settings',
          title: 'Weather Plugin Settings',
          width: 800,
          height: 600,
          html: await this.getSettingsHTML(),
          css: await this.getSettingsCSS(),
          js: await this.getSettingsJS()
        });
      } else {
        // Fallback to modal
        await this.api.modal.open({
          title: 'Weather Plugin Settings',
          content: this.getInlineSettingsHTML(),
          buttons: [
            {
              text: 'Cancel',
              onClick: () => this.api.modal.close()
            },
            {
              text: 'Save',
              type: 'primary',
              onClick: async () => {
                await this.saveSettings();
                this.api.modal.close();
              }
            }
          ]
        });
      }
    } catch (error) {
      await this.logger.error('Failed to show configuration modal', error);
    }
  }

  /**
   * Performs a manual sync of weather data
   * 
   * @returns {Promise<void>}
   */
  async performManualSync() {
    try {
      await this.syncManager.performSync();
    } catch (error) {
      await this.logger.error('Manual sync failed', error);
      throw error;
    }
  }

  /**
   * Shows the weather dashboard
   * 
   * @returns {Promise<void>}
   */
  async showWeatherDashboard() {
    try {
      if (this.api.windows && this.api.windows.open) {
        await this.api.windows.open({
          id: 'weather-dashboard',
          title: 'Weather Dashboard',
          width: 1000,
          height: 800,
          html: await this.getDashboardHTML(),
          css: await this.getDashboardCSS(),
          js: await this.getDashboardJS()
        });
      } else {
        // Fallback to modal
        await this.api.modal.open({
          title: 'Weather Dashboard',
          content: this.getInlineDashboardHTML(),
          width: '90%',
          height: '90%'
        });
      }
    } catch (error) {
      await this.logger.error('Failed to show weather dashboard', error);
    }
  }

  /**
   * Shows the raw weather data view
   * 
   * @returns {Promise<void>}
   */
  async showWeatherDataView() {
    try {
      const weatherData = await this.dataProcessor.getStoredWeatherData();
      
      let content = '<div class="weather-data-view">';
      content += '<h3>Stored Weather Data</h3>';
      
      if (weatherData && weatherData.length > 0) {
        content += '<pre>' + JSON.stringify(weatherData, null, 2) + '</pre>';
      } else {
        content += '<p>No weather data available. Try syncing first.</p>';
      }
      
      content += '</div>';
      
      await this.api.modal.open({
        title: 'Weather Data',
        content: content,
        width: '80%',
        height: '80%'
      });
    } catch (error) {
      await this.logger.error('Failed to show weather data view', error);
    }
  }

  /**
   * Validates the API key with the weather service
   * 
   * @returns {Promise<boolean>} Whether the API key is valid
   */
  async validateAPIKey() {
    try {
      const isValid = await this.weatherAPI.validateAPIKey();
      
      if (isValid) {
        await this.api.notifications.success('API Key is valid');
      } else {
        await this.api.notifications.error('API Key validation failed');
      }
      
      return isValid;
    } catch (error) {
      await this.logger.error('API Key validation error', error);
      await this.api.notifications.error('Error validating API Key: ' + error.message);
      return false;
    }
  }

  /**
   * Gets the current plugin status
   * 
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      lastSync: this.syncManager ? this.syncManager.getLastSyncTime() : null,
      nextSync: this.syncManager ? this.syncManager.getNextSyncTime() : null,
      syncStatus: this.syncManager ? this.syncManager.getSyncStatus() : 'unknown'
    };
  }

  /**
   * Cleans up resources when the plugin is disabled
   * 
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      await this.logger.logLifecycle('weather-plugin-cleanup-start');

      // Cleanup sync manager
      if (this.syncManager) {
        await this.syncManager.cleanup();
      }

      // Remove UI elements
      if (this.ribbonIconId && this.api.ui && this.api.ui.removeRibbonIcon) {
        this.api.ui.removeRibbonIcon(this.ribbonIconId);
      }

      this.isInitialized = false;

      await this.logger.logLifecycle('weather-plugin-cleanup-complete');
    } catch (error) {
      await this.logger.error('Failed during plugin cleanup', error);
    }
  }

  // Helper methods for UI content will be implemented in the next step
  async getSettingsHTML() { return ''; }
  async getSettingsCSS() { return ''; }
  async getSettingsJS() { return ''; }
  async getDashboardHTML() { return ''; }
  async getDashboardCSS() { return ''; }
  async getDashboardJS() { return ''; }
  getInlineSettingsHTML() { return ''; }
  getInlineDashboardHTML() { return ''; }
}

// Export the WeatherPlugin class for testing
module.exports.WeatherPlugin = WeatherPlugin;

// Initialize the plugin
try {
  // Check if PluginAPI is available (may not be in test environment)
  if (typeof PluginAPI !== 'undefined') {
    const plugin = new WeatherPlugin(PluginAPI);

    // Initialize the plugin
    plugin.initialize().catch(async (error) => {
      console.error('Failed to initialize Weather plugin:', error);
      if (plugin.logger) {
        await plugin.logger.error('Failed to initialize Weather plugin', error);
      }
    });

    // Export the plugin for external access
    module.exports.plugin = plugin;
    module.exports.name = 'Weather Plugin';
    module.exports.version = '1.0.0';
    module.exports.getStatus = () => plugin.getStatus();
    module.exports.cleanup = () => plugin.cleanup();
  }
} catch (error) {
  console.error('Error creating Weather plugin:', error);
}
