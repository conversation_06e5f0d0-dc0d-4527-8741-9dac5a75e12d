/**
 * Unit tests for the DataProcessor class
 * 
 * @module DataProcessorTests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const DataProcessor = require('../src/data-processor');
const { Pool } = require('pg');

// Mock the database pool
const pool = new Pool();
sinon.stub(pool, 'query').resolves({ rows: [], rowCount: 0 });

// Mock the database schema
const dbSchema = {
  hasTable: sinon.stub().resolves(false),
  createTable: sinon.stub().resolves()
};

// Mock the database instance
const db = {
  schema: dbSchema,
  raw: (sql) => sql,
  fn: {
    now: () => 'NOW()'
  }
};

describe('DataProcessor', () => {
  let logger;
  let api;
  let dataProcessor;

  beforeEach(() => {
    // Reset all mocks
    sinon.resetHistory();
    
    // Create a mock logger
    logger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    };

    // Create a mock API
    api = {
      database: db,
      user: {
        id: 'test-user-123'
      },
      settings: {
        get: sinon.stub().resolves({
          latitude: 40.7128,
          longitude: -74.0060,
          units: 'metric'
        })
      },
      storage: {
        get: sinon.stub().resolves(null),
        set: sinon.stub().resolves()
      }
    };

    // Create a new instance of DataProcessor for each test
    dataProcessor = new DataProcessor(api, logger);
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await dataProcessor.initialize();
      expect(dbSchema.hasTable.calledOnce).to.be.true;
      expect(dbSchema.createTable.calledOnce).to.be.true;
      expect(logger.debug.calledWith('Data processor initialized')).to.be.true;
    });

    it('should handle table already exists', async () => {
      // Make hasTable return true to simulate table already exists
      dbSchema.hasTable.resolves(true);
      
      await dataProcessor.initialize();
      expect(dbSchema.hasTable.calledOnce).to.be.true;
      expect(dbSchema.createTable.called).to.be.false;
    });
  });

  describe('processWeatherData', () => {
    const sampleWeatherData = {
      current: {
        last_updated_epoch: 1620000000,
        last_updated: '2023-06-01 12:00',
        temp_c: 25,
        temp_f: 77,
        is_day: 1,
        condition: {
          text: 'Sunny',
          icon: '//cdn.weatherapi.com/weather/64x64/day/113.png',
          code: 1000
        },
        wind_mph: 6.2,
        wind_kph: 10,
        wind_degree: 180,
        wind_dir: 'S',
        pressure_mb: 1013,
        pressure_in: 29.92,
        precip_mm: 0,
        precip_in: 0,
        humidity: 60,
        cloud: 0,
        feelslike_c: 26,
        feelslike_f: 78.8,
        vis_km: 10,
        vis_miles: 6,
        uv: 7,
        gust_mph: 10.3,
        gust_kph: 16.6
      },
      forecast: {
        forecastday: [
          {
            date: '2023-06-01',
            date_epoch: 1685577600,
            day: {
              maxtemp_c: 28,
              maxtemp_f: 82.4,
              mintemp_c: 18,
              mintemp_f: 64.4,
              avgtemp_c: 23,
              avgtemp_f: 73.4,
              maxwind_mph: 9.3,
              maxwind_kph: 15,
              totalprecip_mm: 0,
              totalprecip_in: 0,
              totalsnow_cm: 0,
              avgvis_km: 10,
              avgvis_miles: 6,
              avghumidity: 65,
              daily_will_it_rain: 0,
              daily_chance_of_rain: 0,
              daily_will_it_snow: 0,
              daily_chance_of_snow: 0,
              condition: {
                text: 'Sunny',
                icon: '//cdn.weatherapi.com/weather/64x64/day/113.png',
                code: 1000
              },
              uv: 8
            },
            astro: {
              sunrise: '05:30 AM',
              sunset: '08:30 PM',
              moonrise: '10:00 AM',
              moonset: '11:00 PM',
              moon_phase: 'First Quarter',
              moon_illumination: '50',
              is_moon_up: 0,
              is_sun_up: 1
            },
            hour: [
              {
                time_epoch: 1685577600,
                time: '2023-06-01 00:00',
                temp_c: 20,
                temp_f: 68,
                is_day: 0,
                condition: {
                  text: 'Clear',
                  icon: '//cdn.weatherapi.com/weather/64x64/night/113.png',
                  code: 1000
                },
                wind_mph: 5.6,
                wind_kph: 9,
                wind_degree: 180,
                wind_dir: 'S',
                pressure_mb: 1014,
                pressure_in: 29.94,
                precip_mm: 0,
                precip_in: 0,
                humidity: 70,
                cloud: 0,
                feelslike_c: 20,
                feelslike_f: 68,
                windchill_c: 20,
                windchill_f: 68,
                heatindex_c: 22,
                heatindex_f: 71.6,
                dewpoint_c: 14,
                dewpoint_f: 57.2,
                will_it_rain: 0,
                chance_of_rain: 0,
                will_it_snow: 0,
                chance_of_snow: 0,
                vis_km: 10,
                vis_miles: 6,
                gust_mph: 8.3,
                gust_kph: 13.3,
                uv: 1
              }
            ]
          }
        ]
      }
    };

    beforeEach(() => {
      // Reset the database query stub
      pool.query.resetHistory();
      
      // Mock the database query for insert/update operations
      pool.query.resolves({ rowCount: 1 });
      
      // Mock the database schema to say the table exists
      dbSchema.hasTable.resolves(true);
    });

    it('should process current weather data', async () => {
      const result = await dataProcessor.processWeatherData(sampleWeatherData, 'metric');
      
      // Verify the result structure
      expect(result).to.have.property('current');
      expect(result).to.have.property('forecast');
      expect(result).to.have.property('units', 'metric');
      expect(result).to.have.property('lastUpdated');
      
      // Verify current weather data
      const current = result.current;
      expect(current).to.have.property('forecast_date', '2023-06-01');
      expect(current).to.have.property('is_daytime', true);
      expect(current).to.have.property('condition_code', '1000');
      expect(current).to.have.property('temperature', 25);
      expect(current).to.have.property('feels_like', 26);
      expect(current).to.have.property('humidity', 60);
    });

    it('should process forecast data', async () => {
      const result = await dataProcessor.processWeatherData(sampleWeatherData, 'metric');
      
      // Verify forecast data
      expect(result.forecast).to.be.an('array').with.lengthOf(1);
      const forecastDay = result.forecast[0];
      
      expect(forecastDay).to.have.property('forecast_date', '2023-06-01');
      expect(forecastDay).to.have.property('is_daytime', true);
      expect(forecastDay).to.have.property('condition_code', '1000');
      expect(forecastDay).to.have.property('temperature', 23); // avg temp
      expect(forecastDay).to.have.property('feels_like', 23); // avg temp as fallback
      expect(forecastDay).to.have.property('humidity', 65);
      expect(forecastDay).to.have.property('precipitation_chance', 0);
      expect(forecastDay).to.have.property('wind_speed', 15); // max wind in km/h
    });

    it('should store data in the database', async () => {
      await dataProcessor.processWeatherData(sampleWeatherData, 'metric');
      
      // Verify database operations
      expect(pool.query.called).to.be.true;
      
      // Check that we're trying to insert/update records
      const queries = pool.query.getCalls().map(call => call.args[0]);
      expect(queries.some(q => q.includes('INSERT INTO') || q.includes('UPDATE'))).to.be.true;
    });

    it('should handle missing location settings', async () => {
      // Override settings to not have location
      api.settings.get.resolves({});
      
      try {
        await dataProcessor.processWeatherData(sampleWeatherData, 'metric');
        expect.fail('Should have thrown an error for missing location');
      } catch (error) {
        expect(error.message).to.include('Location not configured');
      }
    });

    it('should handle database errors gracefully', async () => {
      // Make the database query fail
      pool.query.rejects(new Error('Database connection failed'));
      
      try {
        await dataProcessor.processWeatherData(sampleWeatherData, 'metric');
        expect.fail('Should have thrown a database error');
      } catch (error) {
        expect(error.message).to.include('Database connection failed');
        expect(logger.error.called).to.be.true;
      }
    });
  });

  describe('getStoredWeatherData', () => {
    const mockWeatherRecords = [
      {
        id: '1',
        user_id: 'test-user-123',
        latitude: 40.7128,
        longitude: -74.0060,
        forecast_date: '2023-06-01',
        forecast_start: '2023-06-01T12:00:00Z',
        forecast_end: '2023-06-01T13:00:00Z',
        is_daytime: true,
        condition_code: '1000',
        temperature: 25,
        feels_like: 26,
        humidity: 60,
        precipitation_chance: 0,
        precipitation_type: null,
        precipitation_amount: 0,
        snowfall_amount: 0,
        wind_speed: 10,
        wind_direction: 180,
        wind_direction_code: 'S',
        pressure: 1013,
        visibility: 10,
        uv_index: 7,
        air_quality: {},
        units: 'metric',
        created_at: '2023-06-01T12:00:00Z',
        updated_at: '2023-06-01T12:00:00Z'
      },
      {
        id: '2',
        user_id: 'test-user-123',
        latitude: 40.7128,
        longitude: -74.0060,
        forecast_date: '2023-06-01',
        forecast_start: '2023-06-01T00:00:00Z',
        forecast_end: '2023-06-01T23:59:59Z',
        is_daytime: true,
        condition_code: '1000',
        temperature: 23,
        feels_like: 23,
        humidity: 65,
        precipitation_chance: 0,
        precipitation_type: null,
        precipitation_amount: 0,
        snowfall_amount: 0,
        wind_speed: 15,
        wind_direction: 0,
        wind_direction_code: 'N/A',
        pressure: 1013.25,
        visibility: 10,
        uv_index: 8,
        air_quality: {},
        units: 'metric',
        created_at: '2023-06-01T00:00:00Z',
        updated_at: '2023-06-01T00:00:00Z'
      }
    ];

    beforeEach(() => {
      // Mock the database to return sample records
      pool.query.resolves({ rows: mockWeatherRecords });
      
      // Mock the database schema to say the table exists
      dbSchema.hasTable.resolves(true);
    });

    it('should retrieve stored weather data', async () => {
      const result = await dataProcessor.getStoredWeatherData({ days: 5 });
      
      // Verify the result structure
      expect(result).to.have.property('current');
      expect(result).to.have.property('forecast');
      
      // Verify current weather data
      const current = result.current;
      expect(current).to.have.property('forecast_date', '2023-06-01');
      expect(current).to.have.property('is_daytime', true);
      expect(current).to.have.property('temperature', 25);
      
      // Verify forecast data
      expect(result.forecast).to.be.an('array').with.lengthOf(1);
      const forecastDay = result.forecast[0];
      expect(forecastDay).to.have.property('forecast_date', '2023-06-01');
      expect(forecastDay).to.have.property('temperature', 23);
    });

    it('should handle empty results', async () => {
      // Mock empty result set
      pool.query.resolves({ rows: [] });
      
      const result = await dataProcessor.getStoredWeatherData({ days: 5 });
      
      expect(result).to.deep.equal({
        current: null,
        forecast: [],
        lastUpdated: null
      });
    });

    it('should handle database errors', async () => {
      // Make the database query fail
      pool.query.rejects(new Error('Database error'));
      
      try {
        await dataProcessor.getStoredWeatherData({ days: 5 });
        expect.fail('Should have thrown a database error');
      } catch (error) {
        expect(error.message).to.include('Database error');
        expect(logger.error.called).to.be.true;
      }
    });
  });

  describe('clearWeatherData', () => {
    it('should clear weather data for the current user', async () => {
      // Mock successful deletion
      pool.query.resolves({ rowCount: 5 });
      
      const result = await dataProcessor.clearWeatherData();
      
      // Should return the number of deleted records
      expect(result).to.equal(5);
      
      // Verify the query was made with the correct user ID
      expect(pool.query.calledOnce).to.be.true;
      const query = pool.query.getCall(0).args[0];
      expect(query).to.include("DELETE FROM weather_forecasts WHERE user_id = 'test-user-123'");
      
      // Verify logging
      expect(logger.info.calledWith('Cleared 5 weather records')).to.be.true;
    });

    it('should handle errors when clearing weather data', async () => {
      // Make the database query fail
      pool.query.rejects(new Error('Database error'));
      
      try {
        await dataProcessor.clearWeatherData();
        expect.fail('Should have thrown a database error');
      } catch (error) {
        expect(error.message).to.include('Database error');
        expect(logger.error.called).to.be.true;
      }
    });
  });
});
