/**
 * Unit tests for the WeatherAPI class
 * 
 * @module WeatherAPITests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const WeatherAPI = require('../src/weather-api');

// Mock the global fetch
const fetch = require('node-fetch');
const { Response } = require('node-fetch');

// Mock the API responses
const mockCurrentWeatherResponse = {
  location: {
    name: 'New York',
    region: 'New York',
    country: 'United States of America',
    lat: 40.71,
    lon: -74.01,
    tz_id: 'America/New_York',
    localtime_epoch: 1625097600,
    localtime: '2023-06-30 12:00'
  },
  current: {
    last_updated_epoch: 1625097600,
    last_updated: '2023-06-30 12:00',
    temp_c: 25,
    temp_f: 77,
    is_day: 1,
    condition: {
      text: 'Sunny',
      icon: '//cdn.weatherapi.com/weather/64x64/day/113.png',
      code: 1000
    },
    wind_mph: 6.2,
    wind_kph: 10,
    wind_degree: 180,
    wind_dir: 'S',
    pressure_mb: 1013,
    pressure_in: 29.92,
    precip_mm: 0,
    precip_in: 0,
    humidity: 60,
    cloud: 0,
    feelslike_c: 26,
    feelslike_f: 78.8,
    vis_km: 10,
    vis_miles: 6,
    uv: 7,
    gust_mph: 8.1,
    gust_kph: 13,
    air_quality: {
      co: 210.3,
      no2: 2.8,
      o3: 68.7,
      so2: 1.2,
      pm2_5: 2.9,
      pm10: 4.5,
      'us-epa-index': 1,
      'gb-defra-index': 1
    }
  }
};

const mockForecastResponse = {
  location: {
    name: 'New York',
    region: 'New York',
    country: 'United States of America',
    lat: 40.71,
    lon: -74.01,
    tz_id: 'America/New_York',
    localtime_epoch: 1625097600,
    localtime: '2023-06-30 12:00'
  },
  current: {
    // Same as mockCurrentWeatherResponse.current
    ...mockCurrentWeatherResponse.current
  },
  forecast: {
    forecastday: [
      {
        date: '2023-06-30',
        date_epoch: 1688083200,
        day: {
          maxtemp_c: 28,
          maxtemp_f: 82.4,
          mintemp_c: 18,
          mintemp_f: 64.4,
          avgtemp_c: 23,
          avgtemp_f: 73.4,
          maxwind_mph: 9.3,
          maxwind_kph: 15,
          totalprecip_mm: 0,
          totalprecip_in: 0,
          totalsnow_cm: 0,
          avgvis_km: 10,
          avgvis_miles: 6,
          avghumidity: 65,
          daily_will_it_rain: 0,
          daily_chance_of_rain: 0,
          daily_will_it_snow: 0,
          daily_chance_of_snow: 0,
          condition: {
            text: 'Sunny',
            icon: '//cdn.weatherapi.com/weather/64x64/day/113.png',
            code: 1000
          },
          uv: 8,
          air_quality: {
            co: 213.6,
            no2: 2.8,
            o3: 68.7,
            so2: 1.2,
            pm2_5: 2.9,
            pm10: 4.5,
            'us-epa-index': 1,
            'gb-defra-index': 1
          }
        },
        astro: {
          sunrise: '05:30 AM',
          sunset: '08:30 PM',
          moonrise: '10:00 AM',
          moonset: '11:00 PM',
          moon_phase: 'First Quarter',
          moon_illumination: '50',
          is_moon_up: 0,
          is_sun_up: 1
        },
        hour: [
          {
            time_epoch: 1688083200,
            time: '2023-06-30 00:00',
            temp_c: 18,
            temp_f: 64.4,
            is_day: 0,
            condition: {
              text: 'Clear',
              icon: '//cdn.weatherapi.com/weather/64x64/night/113.png',
              code: 1000
            },
            wind_mph: 4.3,
            wind_kph: 6.8,
            wind_degree: 180,
            wind_dir: 'S',
            pressure_mb: 1013,
            pressure_in: 29.92,
            precip_mm: 0,
            precip_in: 0,
            humidity: 70,
            cloud: 0,
            feelslike_c: 18,
            feelslike_f: 64.4,
            windchill_c: 18,
            windchill_f: 64.4,
            heatindex_c: 18,
            heatindex_f: 64.4,
            dewpoint_c: 12.5,
            dewpoint_f: 54.5,
            will_it_rain: 0,
            chance_of_rain: 0,
            will_it_snow: 0,
            chance_of_snow: 0,
            vis_km: 10,
            vis_miles: 6,
            gust_mph: 6.5,
            gust_kph: 10.4,
            uv: 1
          }
        ]
      }
    ]
  }
};

describe('WeatherAPI', () => {
  let logger;
  let weatherAPI;
  let fetchStub;

  beforeEach(() => {
    // Reset all mocks
    sinon.resetHistory();
    
    // Create a mock logger
    logger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    };

    // Create a new instance of WeatherAPI for each test
    weatherAPI = new WeatherAPI('test-api-key', logger);
    
    // Stub the fetch function
    fetchStub = sinon.stub(global, 'fetch');
  });

  afterEach(() => {
    // Restore the original fetch function
    fetchStub.restore();
  });

  describe('Constructor', () => {
    it('should initialize with API key', () => {
      expect(weatherAPI.apiKey).to.equal('test-api-key');
      expect(weatherAPI.baseUrl).to.equal('https://weatherapi-com.p.rapidapi.com');
      expect(weatherAPI.headers).to.deep.equal({
        'X-RapidAPI-Key': 'test-api-key',
        'X-RapidAPI-Host': 'weatherapi-com.p.rapidapi.com'
      });
    });

    it('should throw error if API key is missing', () => {
      expect(() => new WeatherAPI()).to.throw('API key is required');
      expect(() => new WeatherAPI('')).to.throw('API key is required');
    });
  });

  describe('_makeRequest', () => {
    it('should make a GET request with correct parameters', async () => {
      // Mock a successful response
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
      fetchStub.resolves(mockResponse);
      
      // Make the request
      const result = await weatherAPI._makeRequest('/test', { param: 'value' });
      
      // Verify the fetch was called with the correct parameters
      expect(fetchStub.calledOnce).to.be.true;
      const [url, options] = fetchStub.firstCall.args;
      expect(url).to.include('https://weatherapi-com.p.rapidapi.com/test?param=value');
      expect(options).to.deep.equal({
        method: 'GET',
        headers: {
          'X-RapidAPI-Key': 'test-api-key',
          'X-RapidAPI-Host': 'weatherapi-com.p.rapidapi.com'
        }
      });
      
      // Verify the response was parsed correctly
      expect(result).to.deep.equal({ data: 'test' });
    });

    it('should handle API errors', async () => {
      // Mock an error response
      const mockResponse = new Response(JSON.stringify({
        error: {
          code: 1006,
          message: 'No matching location found.'
        }
      }), {
        status: 400,
        statusText: 'Bad Request',
        headers: { 'Content-Type': 'application/json' }
      });
      fetchStub.resolves(mockResponse);
      
      // Make the request and expect it to throw
      try {
        await weatherAPI._makeRequest('/test');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('No matching location found.');
        expect(error.code).to.equal(1006);
      }
    });

    it('should handle network errors', async () => {
      // Simulate a network error
      fetchStub.rejects(new Error('Network error'));
      
      // Make the request and expect it to throw
      try {
        await weatherAPI._makeRequest('/test');
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('Network error');
      }
    });
  });

  describe('validateApiKey', () => {
    it('should validate a valid API key', async () => {
      // Mock a successful response
      const mockResponse = new Response(JSON.stringify({
        valid: true
      }), { status: 200 });
      fetchStub.resolves(mockResponse);
      
      // Validate the API key
      const isValid = await weatherAPI.validateApiKey();
      
      // Should return true for a valid key
      expect(isValid).to.be.true;
      
      // Should have made a request to the validation endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/current.json?q=40.71,-74.01');
    });

    it('should detect an invalid API key', async () => {
      // Mock an unauthorized response
      const mockResponse = new Response(JSON.stringify({
        error: {
          code: 2008,
          message: 'API key has been disabled.'
        }
      }), { 
        status: 403,
        statusText: 'Forbidden'
      });
      fetchStub.resolves(mockResponse);
      
      // Validate the API key
      const isValid = await weatherAPI.validateApiKey();
      
      // Should return false for an invalid key
      expect(isValid).to.be.false;
    });
  });

  describe('getCurrentWeather', () => {
    it('should fetch current weather for coordinates', async () => {
      // Mock a successful response
      const mockResponse = new Response(
        JSON.stringify(mockCurrentWeatherResponse),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get current weather
      const weather = await weatherAPI.getCurrentWeather(40.71, -74.01);
      
      // Should return the current weather data
      expect(weather).to.deep.equal(mockCurrentWeatherResponse);
      
      // Should have made a request to the current weather endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/current.json');
      expect(url).to.include('q=40.71,-74.01');
      expect(url).to.include('aqi=yes');
    });

    it('should fetch current weather for a city name', async () => {
      // Mock a successful response
      const mockResponse = new Response(
        JSON.stringify(mockCurrentWeatherResponse),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get current weather by city name
      await weatherAPI.getCurrentWeather('New York');
      
      // Should have made a request with the city name
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('q=New%20York');
    });

    it('should include air quality data when requested', async () => {
      // Mock a successful response
      const mockResponse = new Response(
        JSON.stringify(mockCurrentWeatherResponse),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get current weather with air quality
      await weatherAPI.getCurrentWeather(40.71, -74.01, true);
      
      // Should include aqi=yes in the URL
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('aqi=yes');
    });
  });

  describe('getForecast', () => {
    it('should fetch forecast for coordinates', async () => {
      // Mock a successful response
      const mockResponse = new Response(
        JSON.stringify(mockForecastResponse),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get forecast
      const forecast = await weatherAPI.getForecast(40.71, -74.01, 3);
      
      // Should return the forecast data
      expect(forecast).to.deep.equal(mockForecastResponse);
      
      // Should have made a request to the forecast endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/forecast.json');
      expect(url).to.include('q=40.71,-74.01');
      expect(url).to.include('days=3');
      expect(url).to.include('aqi=yes');
      expect(url).to.include('alerts=yes');
    });

    it('should fetch forecast with custom parameters', async () => {
      // Mock a successful response
      const mockResponse = new Response(
        JSON.stringify(mockForecastResponse),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get forecast with custom parameters
      await weatherAPI.getForecast('New York', 5, false, false);
      
      // Should include the custom parameters in the URL
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('days=5');
      expect(url).to.include('aqi=no');
      expect(url).to.include('alerts=no');
    });
  });

  describe('getWeatherData', () => {
    it('should fetch both current weather and forecast', async () => {
      // Mock successful responses for both endpoints
      const mockCurrentResponse = new Response(
        JSON.stringify(mockCurrentWeatherResponse),
        { status: 200 }
      );
      
      const mockForecastResponseObj = new Response(
        JSON.stringify(mockForecastResponse),
        { status: 200 }
      );
      
      // Stub fetch to return different responses for different URLs
      fetchStub.onFirstCall().resolves(mockCurrentResponse);
      fetchStub.onSecondCall().resolves(mockForecastResponseObj);
      
      // Get weather data
      const weatherData = await weatherAPI.getWeatherData(40.71, -74.01, 3);
      
      // Should return combined data
      expect(weatherData).to.have.property('location');
      expect(weatherData).to.have.property('current');
      expect(weatherData).to.have.property('forecast');
      
      // Should have made two requests
      expect(fetchStub.calledTwice).to.be.true;
      
      // First call should be to current weather
      const [firstUrl] = fetchStub.firstCall.args;
      expect(firstUrl).to.include('/current.json');
      
      // Second call should be to forecast
      const [secondUrl] = fetchStub.secondCall.args;
      expect(secondUrl).to.include('/forecast.json');
    });

    it('should handle errors from one of the endpoints', async () => {
      // Mock a successful current weather response but a failed forecast response
      const mockCurrentResponse = new Response(
        JSON.stringify(mockCurrentWeatherResponse),
        { status: 200 }
      );
      
      const errorResponse = new Response(JSON.stringify({
        error: {
          code: 1006,
          message: 'No matching location found.'
        }
      }), { 
        status: 400,
        statusText: 'Bad Request'
      });
      
      // Stub fetch to return different responses for different URLs
      fetchStub.onFirstCall().resolves(mockCurrentResponse);
      fetchStub.onSecondCall().resolves(errorResponse);
      
      // Get weather data and expect it to throw
      try {
        await weatherAPI.getWeatherData(40.71, -74.01, 3);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('No matching location found');
      }
    });
  });

  describe('searchLocations', () => {
    it('should search for locations by query', async () => {
      // Mock a successful response
      const mockLocations = [
        {
          id: 2801268,
          name: 'London',
          region: 'City of London, Greater London',
          country: 'United Kingdom',
          lat: 51.52,
          lon: -0.11,
          url: 'london-city-of-london-greater-london-united-kingdom'
        }
      ];
      
      const mockResponse = new Response(
        JSON.stringify(mockLocations),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Search for locations
      const locations = await weatherAPI.searchLocations('london');
      
      // Should return the locations
      expect(locations).to.deep.equal(mockLocations);
      
      // Should have made a request to the search endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/search.json');
      expect(url).to.include('q=london');
    });
  });

  describe('getAstronomy', () => {
    it('should fetch astronomy data for a location and date', async () => {
      // Mock a successful response
      const mockAstronomy = {
        astronomy: {
          astro: {
            sunrise: '06:30 AM',
            sunset: '07:30 PM',
            moonrise: '02:00 PM',
            moonset: '02:00 AM',
            moon_phase: 'Waxing Crescent',
            moon_illumination: '45'
          }
        }
      };
      
      const mockResponse = new Response(
        JSON.stringify(mockAstronomy),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get astronomy data
      const astronomy = await weatherAPI.getAstronomy(40.71, -74.01, '2023-06-30');
      
      // Should return the astronomy data
      expect(astronomy).to.deep.equal(mockAstronomy.astronomy);
      
      // Should have made a request to the astronomy endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/astronomy.json');
      expect(url).to.include('q=40.71,-74.01');
      expect(url).to.include('dt=2023-06-30');
    });
  });

  describe('getTimeZone', () => {
    it('should fetch timezone information for a location', async () => {
      // Mock a successful response
      const mockTimeZone = {
        location: {
          name: 'New York',
          region: 'New York',
          country: 'United States of America',
          lat: 40.71,
          lon: -74.01,
          tz_id: 'America/New_York',
          localtime_epoch: 1625097600,
          localtime: '2023-06-30 12:00'
        }
      };
      
      const mockResponse = new Response(
        JSON.stringify(mockTimeZone),
        { status: 200 }
      );
      fetchStub.resolves(mockResponse);
      
      // Get timezone information
      const timeZone = await weatherAPI.getTimeZone(40.71, -74.01);
      
      // Should return the timezone data
      expect(timeZone).to.deep.equal(mockTimeZone.location);
      
      // Should have made a request to the timezone endpoint
      expect(fetchStub.calledOnce).to.be.true;
      const [url] = fetchStub.firstCall.args;
      expect(url).to.include('/timezone.json');
      expect(url).to.include('q=40.71,-74.01');
    });
  });
});
