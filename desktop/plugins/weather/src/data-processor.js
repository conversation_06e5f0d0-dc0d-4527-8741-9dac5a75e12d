/**
 * Data Processor for Weather Plugin
 * 
 * Handles processing, normalizing, and storing weather data.
 * Manages database operations and unit conversions.
 * 
 * @module DataProcessor
 */

/**
 * Data Processor class for handling weather data operations
 * 
 * @class DataProcessor
 */
class DataProcessor {
  /**
   * Creates a new DataProcessor instance
   * 
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.tableName = 'weather_forecasts';
  }

  /**
   * Initializes the data processor and ensures the database table exists
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this._ensureTableExists();
      await this.logger.debug('Data processor initialized');
    } catch (error) {
      await this.logger.error('Failed to initialize data processor:', error);
      throw error;
    }
  }

  /**
   * Ensures the weather_forecasts table exists in the database
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _ensureTableExists() {
    const tableExists = await this.api.database.schema.hasTable(this.tableName);
    
    if (!tableExists) {
      await this.logger.debug('Creating weather_forecasts table');
      
      await this.api.database.schema.createTable(this.tableName, (table) => {
        table.uuid('id').primary().defaultTo(this.api.database.raw('uuid_generate_v4()'));
        table.uuid('user_id').references('id').inTable('auth.users').onDelete('CASCADE');
        table.decimal('latitude', 10, 6).notNullable();
        table.decimal('longitude', 10, 6).notNullable();
        table.date('forecast_date').notNullable();
        table.timestamp('forecast_start').notNullable();
        table.timestamp('forecast_end').notNullable();
        table.boolean('is_daytime').notNullable();
        table.string('condition_code', 50).notNullable();
        table.decimal('temperature', 5, 2).notNullable();
        table.decimal('feels_like', 5, 2).notNullable();
        table.decimal('humidity', 5, 2).notNullable();
        table.decimal('precipitation_chance', 5, 2).notNullable();
        table.string('precipitation_type', 50);
        table.decimal('precipitation_amount', 8, 2).notNullable().defaultTo(0);
        table.decimal('snowfall_amount', 8, 2).notNullable().defaultTo(0);
        table.decimal('wind_speed', 8, 2).notNullable();
        table.integer('wind_direction').notNullable();
        table.string('wind_direction_code', 3).notNullable();
        table.decimal('pressure', 8, 2).notNullable();
        table.decimal('visibility', 8, 2).notNullable();
        table.decimal('uv_index', 5, 2).notNullable();
        table.jsonb('air_quality').defaultTo('{}');
        table.string('units', 10).notNullable().defaultTo('metric');
        table.timestamp('created_at').defaultTo(this.api.database.fn.now());
        table.timestamp('updated_at').defaultTo(this.api.database.fn.now());
        
        // Indexes for faster lookups
        table.index(['user_id', 'forecast_date', 'is_daytime']);
        table.index(['latitude', 'longitude', 'forecast_date']);
      });
      
      await this.logger.info('Created weather_forecasts table');
    }
  }

  /**
   * Processes raw weather data and stores it in the database
   * 
   * @param {Object} weatherData - Raw weather data from the API
   * @param {string} [units='metric'] - Units to use ('metric' or 'imperial')
   * @returns {Promise<Object>} Processed weather data
   */
  async processWeatherData(weatherData, units = 'metric') {
    try {
      await this.logger.debug('Processing weather data');
      
      // Get current user ID
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      // Get current settings for location
      const settings = await this.api.settings.get();
      if (!settings.latitude || !settings.longitude) {
        throw new Error('Location not configured');
      }
      
      // Process current weather
      const currentData = this._processCurrentWeather(weatherData.current, units);
      
      // Process forecast data
      const forecastData = this._processForecast(weatherData.forecast, units);
      
      // Combine and store data
      const allData = [currentData, ...forecastData];
      
      // Store in database
      await this._storeWeatherData(allData, userId, units);
      
      return {
        current: currentData,
        forecast: forecastData,
        units,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      await this.logger.error('Failed to process weather data:', error);
      throw error;
    }
  }

  /**
   * Processes current weather data
   * 
   * @private
   * @param {Object} current - Current weather data
   * @param {string} units - Units to use ('metric' or 'imperial')
   * @returns {Object} Processed current weather data
   */
  _processCurrentWeather(current, units) {
    const isDaytime = current.isDay;
    const forecastDate = new Date(current.lastUpdated);
    
    return {
      forecast_date: this._formatDate(forecastDate),
      forecast_start: forecastDate.toISOString(),
      forecast_end: new Date(forecastDate.getTime() + 3600000).toISOString(), // 1 hour window
      is_daytime: isDaytime,
      condition_code: current.condition.code.toString(),
      temperature: units === 'metric' ? current.tempC : current.tempF,
      feels_like: units === 'metric' ? current.feelslikeC : current.feelslikeF,
      humidity: current.humidity,
      precipitation_chance: current.precip_mm > 0 ? 100 : 0, // Simplified for current weather
      precipitation_type: this._getPrecipitationType(current.condition.code, current.precip_mm),
      precipitation_amount: units === 'metric' ? current.precip_mm : current.precip_in,
      snowfall_amount: 0, // Not directly available in current weather
      wind_speed: units === 'metric' ? current.windKph : current.windMph,
      wind_direction: current.windDegree,
      wind_direction_code: current.windDir,
      pressure: units === 'metric' ? current.pressureMb : current.pressureIn,
      visibility: units === 'metric' ? current.visKm : current.visMiles,
      uv_index: current.uv,
      air_quality: current.airQuality || {}
    };
  }

  /**
   * Processes forecast data
   * 
   * @private
   * @param {Array} forecast - Forecast data
   * @param {string} units - Units to use ('metric' or 'imperial')
   * @returns {Array} Processed forecast data
   */
  _processForecast(forecast, units) {
    const processedDays = [];
    
    for (const day of forecast) {
      // Process day forecast
      processedDays.push({
        forecast_date: day.date,
        forecast_start: new Date(day.date + 'T00:00:00').toISOString(),
        forecast_end: new Date(day.date + 'T23:59:59').toISOString(),
        is_daytime: true, // Day forecast
        condition_code: day.day.condition.code.toString(),
        temperature: units === 'metric' ? day.day.avgtempC : day.day.avgtempF,
        feels_like: units === 'metric' ? day.day.avgtempC : day.day.avgtempF, // Approximate
        humidity: day.day.avghumidity,
        precipitation_chance: day.day.dailyChanceOfRain,
        precipitation_type: this._getPrecipitationType(day.day.condition.code, day.day.totalprecip_mm),
        precipitation_amount: units === 'metric' ? day.day.totalprecip_mm : day.day.totalprecip_in,
        snowfall_amount: day.day.totalsnow_cm || 0,
        wind_speed: units === 'metric' ? day.day.maxwindKph : day.day.maxwindMph,
        wind_direction: 0, // Not available in daily forecast
        wind_direction_code: 'N/A',
        pressure: units === 'metric' ? 1013.25 : 29.92, // Default sea level pressure
        visibility: units === 'metric' ? 10 : 6.2, // Default visibility
        uv_index: day.day.uv,
        air_quality: {}
      });
      
      // Process hourly forecast (optional, can be enabled if needed)
      // for (const hour of day.hour) {
      //   // Process each hour
      // }
    }
    
    return processedDays;
  }

  /**
   * Stores processed weather data in the database
   * 
   * @private
   * @param {Array} weatherData - Processed weather data
   * @param {string} userId - User ID
   * @param {string} units - Units used ('metric' or 'imperial')
   * @returns {Promise<void>}
   */
  async _storeWeatherData(weatherData, userId, units) {
    const trx = await this.api.database.transaction();
    
    try {
      for (const data of weatherData) {
        // Check if record exists
        const existing = await trx(this.tableName)
          .where({
            user_id: userId,
            forecast_date: data.forecast_date,
            is_daytime: data.is_daytime
          })
          .first();
        
        const record = {
          ...data,
          user_id: userId,
          units,
          updated_at: this.api.database.fn.now()
        };
        
        if (existing) {
          // Update existing record
          await trx(this.tableName)
            .where('id', existing.id)
            .update(record);
        } else {
          // Insert new record
          await trx(this.tableName).insert(record);
        }
      }
      
      await trx.commit();
      await this.logger.debug(`Stored ${weatherData.length} weather records`);
    } catch (error) {
      await trx.rollback();
      await this.logger.error('Failed to store weather data:', error);
      throw error;
    }
  }

  /**
   * Gets the stored weather data for the current user
   * 
   * @param {Object} [options] - Query options
   * @param {number} [options.days=5] - Number of days to retrieve
   * @param {boolean} [options.includeCurrent=true] - Whether to include current weather
   * @returns {Promise<Object>} Stored weather data
   */
  async getStoredWeatherData({ days = 5, includeCurrent = true } = {}) {
    try {
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - (days - 1));
      
      // Format dates for query
      const startDateStr = this._formatDate(startDate);
      const endDateStr = this._formatDate(endDate);
      
      // Build query
      let query = this.api.database(this.tableName)
        .where('user_id', userId)
        .whereBetween('forecast_date', [startDateStr, endDateStr])
        .orderBy('forecast_date', 'asc')
        .orderBy('is_daytime', 'desc');
      
      if (!includeCurrent) {
        // Exclude current weather (which has a specific time range)
        query = query.whereRaw('forecast_start < forecast_end');
      }
      
      const results = await query;
      
      // Transform results into a more usable format
      return this._transformStoredData(results);
    } catch (error) {
      await this.logger.error('Failed to retrieve stored weather data:', error);
      throw error;
    }
  }

  /**
   * Transforms stored database records into a more usable format
   * 
   * @private
   * @param {Array} records - Database records
   * @returns {Object} Transformed weather data
   */
  _transformStoredData(records) {
    if (!records || records.length === 0) {
      return {
        current: null,
        forecast: [],
        lastUpdated: null
      };
    }
    
    // Get current weather (most recent record with specific time range)
    const currentRecord = records.find(r => 
      r.forecast_start !== r.forecast_end && 
      new Date(r.forecast_start) <= new Date() && 
      new Date(r.forecast_end) >= new Date()
    );
    
    // Group by date for forecast
    const forecastByDate = {};
    records.forEach(record => {
      if (record.forecast_start === record.forecast_end) {
        // Daily forecast
        if (!forecastByDate[record.forecast_date]) {
          forecastByDate[record.forecast_date] = {
            ...record,
            hourly: []
          };
        } else {
          // Merge with existing daily data
          forecastByDate[record.forecast_date] = {
            ...forecastByDate[record.forecast_date],
            ...record
          };
        }
      } else {
        // Hourly forecast
        const date = record.forecast_date;
        if (!forecastByDate[date]) {
          forecastByDate[date] = {
            forecast_date: date,
            hourly: []
          };
        }
        forecastByDate[date].hourly.push(record);
      }
    });
    
    // Convert to array and sort by date
    const forecast = Object.values(forecastByDate)
      .sort((a, b) => new Date(a.forecast_date) - new Date(b.forecast_date));
    
    return {
      current: currentRecord || null,
      forecast,
      lastUpdated: records[0]?.updated_at || null,
      units: records[0]?.units || 'metric'
    };
  }

  /**
   * Gets the precipitation type based on condition code and amount
   * 
   * @private
   * @param {number} conditionCode - Weather condition code
   * @param {number} amount - Precipitation amount in mm
   * @returns {string} Precipitation type ('rain', 'snow', 'sleet', or null)
   */
  _getPrecipitationType(conditionCode, amount) {
    if (!amount || amount <= 0) return null;
    
    // These condition codes indicate snow
    const snowCodes = [
      1063, 1066, 1069, 1072, 1114, 1117, 1147, 1168, 1171, 1198, 1201, 
      1204, 1207, 1210, 1213, 1216, 1219, 1222, 1225, 1237, 1249, 1252, 1255, 1258, 1261, 1264
    ];
    
    // These condition codes indicate sleet/freezing rain
    const sleetCodes = [1069, 1072, 1168, 1171, 1204, 1207, 1249, 1252];
    
    if (sleetCodes.includes(conditionCode)) {
      return 'sleet';
    } else if (snowCodes.includes(conditionCode)) {
      return 'snow';
    } else {
      return 'rain';
    }
  }

  /**
   * Formats a date as YYYY-MM-DD
   * 
   * @private
   * @param {Date} date - Date to format
   * @returns {string} Formatted date string
   */
  _formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Clears all weather data for the current user
   * 
   * @returns {Promise<number>} Number of records deleted
   */
  async clearWeatherData() {
    try {
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      const count = await this.api.database(this.tableName)
        .where('user_id', userId)
        .del();
      
      await this.logger.info(`Cleared ${count} weather records`);
      return count;
    } catch (error) {
      await this.logger.error('Failed to clear weather data:', error);
      throw error;
    }
  }
}

module.exports = DataProcessor;
