/**
 * Sync Manager for Weather Plugin
 * 
 * Handles scheduling and execution of weather data synchronization.
 * Manages background jobs, retries, and sync status.
 * 
 * @module SyncManager
 */

/**
 * Sync Manager class for handling weather data synchronization
 * 
 * @class SyncManager
 */
class SyncManager {
  /**
   * Creates a new SyncManager instance
   * 
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   * @param {Object} weatherAPI - The WeatherAPI instance
   * @param {Object} dataProcessor - The DataProcessor instance
   */
  constructor(api, logger, weatherAPI, dataProcessor) {
    this.api = api;
    this.logger = logger;
    this.weatherAPI = weatherAPI;
    this.dataProcessor = dataProcessor;
    
    // Sync state
    this.syncInterval = null;
    this.lastSyncTime = null;
    this.nextSyncTime = null;
    this.isSyncing = false;
    this.syncStatus = 'idle'; // 'idle' | 'syncing' | 'error'
    this.syncError = null;
    this.retryCount = 0;
    this.maxRetries = 3;
    this.retryDelay = 5 * 60 * 1000; // 5 minutes in milliseconds
    
    // Default sync interval (12 hours in milliseconds)
    this.defaultSyncInterval = 12 * 60 * 60 * 1000;
  }

  /**
   * Initializes the sync manager
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Load sync state from storage if available
      await this._loadSyncState();
      
      // Start auto-sync if enabled in settings
      const settings = await this.api.settings.get();
      if (settings?.autoSync !== false) { // Default to true if not set
        await this.startAutoSync();
      }
      
      await this.logger.debug('Sync manager initialized');
    } catch (error) {
      await this.logger.error('Failed to initialize sync manager:', error);
      throw error;
    }
  }

  /**
   * Starts automatic synchronization based on the configured interval
   * 
   * @param {number} [intervalMs] - Optional custom interval in milliseconds
   * @returns {Promise<void>}
   */
  async startAutoSync(intervalMs) {
    try {
      // Clear any existing interval
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
      
      // Get interval from settings or use default
      const settings = await this.api.settings.get();
      const syncIntervalMs = intervalMs || 
                           (settings?.syncInterval ? settings.syncInterval * 1000 : null) || 
                           this.defaultSyncInterval;
      
      // Set up the interval
      this.syncInterval = setInterval(() => {
        this.performSync().catch(error => {
          this.logger.error('Scheduled sync failed:', error);
        });
      }, syncIntervalMs);
      
      // Update next sync time
      this.nextSyncTime = new Date(Date.now() + syncIntervalMs);
      
      // Perform initial sync if needed
      if (!this.lastSyncTime || (Date.now() - new Date(this.lastSyncTime).getTime() > syncIntervalMs)) {
        await this.performSync();
      }
      
      await this.logger.info(`Auto-sync started with ${Math.round(syncIntervalMs / (60 * 1000))} minute interval`);
    } catch (error) {
      await this.logger.error('Failed to start auto-sync:', error);
      throw error;
    }
  }

  /**
   * Stops automatic synchronization
   * 
   * @returns {Promise<void>}
   */
  async stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      this.nextSyncTime = null;
      await this.logger.info('Auto-sync stopped');
    }
  }

  /**
   * Performs a manual weather data synchronization
   * 
   * @param {Object} [options] - Sync options
   * @param {boolean} [options.force=false] - Whether to force sync even if recently updated
   * @returns {Promise<Object>} Sync result
   */
  async performSync(options = {}) {
    const { force = false } = options;
    
    // Check if already syncing
    if (this.isSyncing) {
      await this.logger.debug('Sync already in progress, skipping');
      return { success: false, message: 'Sync already in progress' };
    }
    
    // Check if we're in a cooldown period (unless forced)
    if (!force && this.lastSyncTime) {
      const timeSinceLastSync = Date.now() - new Date(this.lastSyncTime).getTime();
      const minSyncInterval = 5 * 60 * 1000; // 5 minutes minimum between syncs
      
      if (timeSinceLastSync < minSyncInterval) {
        await this.logger.debug('Skipping sync, too soon since last sync');
        return { 
          success: false, 
          message: 'Skipped: Minimum sync interval not reached',
          nextSync: new Date(Date.now() + (minSyncInterval - timeSinceLastSync))
        };
      }
    }
    
    // Start sync
    this.isSyncing = true;
    this.syncStatus = 'syncing';
    this.syncError = null;
    
    try {
      await this.logger.info('Starting weather data sync');
      
      // Get settings
      const settings = await this.api.settings.get();
      
      // Validate required settings
      if (!settings?.apiKey) {
        throw new Error('Weather API key not configured');
      }
      
      if (settings.latitude === undefined || settings.longitude === undefined) {
        throw new Error('Location not configured');
      }
      
      // Get units from settings (default to metric)
      const units = settings.units || 'metric';
      
      // Fetch fresh weather data
      const weatherData = await this.weatherAPI.getWeatherData(
        settings.latitude,
        settings.longitude,
        5 // 5-day forecast
      );
      
      // Process and store the data
      await this.dataProcessor.processWeatherData(weatherData, units);
      
      // Update sync state
      this.lastSyncTime = new Date().toISOString();
      this.syncStatus = 'idle';
      this.retryCount = 0; // Reset retry count on success
      
      // Update next sync time if we have an interval
      if (this.syncInterval) {
        const interval = settings?.syncInterval ? settings.syncInterval * 1000 : this.defaultSyncInterval;
        this.nextSyncTime = new Date(Date.now() + interval);
      }
      
      // Save sync state
      await this._saveSyncState();
      
      // Notify listeners
      this._notifySyncComplete(true);
      
      await this.logger.info('Weather data sync completed successfully');
      
      return { 
        success: true, 
        message: 'Sync completed successfully',
        lastSync: this.lastSyncTime,
        nextSync: this.nextSyncTime
      };
      
    } catch (error) {
      // Handle sync error
      this.syncStatus = 'error';
      this.syncError = error.message;
      this.retryCount++;
      
      // Check if we should retry
      if (this.retryCount <= this.maxRetries) {
        const retryIn = this.retryCount * this.retryDelay; // Exponential backoff
        await this.logger.warn(
          `Sync failed (attempt ${this.retryCount}/${this.maxRetries}), retrying in ${retryIn / 1000} seconds`,
          error
        );
        
        // Schedule retry
        setTimeout(() => {
          this.performSync(options).catch(err => {
            this.logger.error('Retry sync failed:', err);
          });
        }, retryIn);
        
        return { 
          success: false, 
          message: `Sync failed, retrying in ${retryIn / 1000} seconds (attempt ${this.retryCount}/${this.maxRetries})`,
          error: error.message,
          willRetry: true,
          retryIn: retryIn
        };
      } else {
        // Max retries reached
        await this.logger.error(
          `Sync failed after ${this.maxRetries} attempts, giving up`,
          error
        );
        
        // Save error state
        await this._saveSyncState();
        
        // Notify listeners
        this._notifySyncComplete(false, error);
        
        return { 
          success: false, 
          message: `Sync failed after ${this.maxRetries} attempts`,
          error: error.message,
          willRetry: false
        };
      }
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Gets the current sync status
   * 
   * @returns {Object} Sync status information
   */
  getSyncStatus() {
    return {
      status: this.syncStatus,
      isSyncing: this.isSyncing,
      lastSync: this.lastSyncTime,
      nextSync: this.nextSyncTime,
      error: this.syncError,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries
    };
  }

  /**
   * Gets the last sync time
   * 
   * @returns {Date|null} Last sync time or null if never synced
   */
  getLastSyncTime() {
    return this.lastSyncTime ? new Date(this.lastSyncTime) : null;
  }

  /**
   * Gets the next scheduled sync time
   * 
   * @returns {Date|null} Next sync time or null if not scheduled
   */
  getNextSyncTime() {
    return this.nextSyncTime ? new Date(this.nextSyncTime) : null;
  }

  /**
   * Loads sync state from persistent storage
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _loadSyncState() {
    try {
      const state = await this.api.storage.get('syncState');
      if (state) {
        this.lastSyncTime = state.lastSyncTime || null;
        this.nextSyncTime = state.nextSyncTime ? new Date(state.nextSyncTime) : null;
        this.syncStatus = state.syncStatus || 'idle';
        this.syncError = state.syncError || null;
        this.retryCount = state.retryCount || 0;
        
        await this.logger.debug('Loaded sync state from storage');
      }
    } catch (error) {
      await this.logger.error('Failed to load sync state:', error);
      // Continue with default values
    }
  }

  /**
   * Saves sync state to persistent storage
   * 
   * @private
   * @returns {Promise<void>}
   */
  async _saveSyncState() {
    try {
      const state = {
        lastSyncTime: this.lastSyncTime,
        nextSyncTime: this.nextSyncTime ? this.nextSyncTime.toISOString() : null,
        syncStatus: this.syncStatus,
        syncError: this.syncError,
        retryCount: this.retryCount
      };
      
      await this.api.storage.set('syncState', state);
      await this.logger.debug('Saved sync state to storage');
    } catch (error) {
      await this.logger.error('Failed to save sync state:', error);
    }
  }

  /**
   * Notifies listeners that a sync has completed
   * 
   * @private
   * @param {boolean} success - Whether the sync was successful
   * @param {Error} [error] - Error if sync failed
   */
  _notifySyncComplete(success, error = null) {
    // Emit event that UI can listen to
    if (this.api.events && this.api.events.emit) {
      this.api.events.emit('weather:sync-complete', { 
        success, 
        error: error ? error.message : null,
        lastSync: this.lastSyncTime,
        nextSync: this.nextSyncTime
      });
    }
    
    // Show notification to user
    if (this.api.notifications) {
      if (success) {
        this.api.notifications.show({
          title: 'Weather Data Updated',
          message: 'Your weather data has been successfully updated',
          type: 'success',
          timeout: 5000
        });
      } else if (this.retryCount >= this.maxRetries) {
        this.api.notifications.show({
          title: 'Weather Sync Failed',
          message: `Failed to update weather data after ${this.maxRetries} attempts. ${error?.message || ''}`,
          type: 'error',
          timeout: 10000
        });
      }
    }
  }

  /**
   * Cleans up resources when the plugin is unloaded
   * 
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      // Stop any running syncs
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
      
      // Save current state
      await this._saveSyncState();
      
      await this.logger.debug('Sync manager cleaned up');
    } catch (error) {
      await this.logger.error('Error during sync manager cleanup:', error);
    }
  }
}

module.exports = SyncManager;
