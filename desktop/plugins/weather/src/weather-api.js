/**
 * Weather API Integration
 * 
 * Handles all interactions with the external weather API.
 * Manages authentication, request formatting, and response processing.
 * 
 * @module WeatherAPI
 */

/**
 * Weather API class for interacting with the external weather service
 * 
 * @class WeatherAPI
 */
class WeatherAPI {
  /**
   * Creates a new WeatherAPI instance
   * 
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.baseUrl = 'https://weatherapi-com.p.rapidapi.com';
    this.rapidApiHost = 'weatherapi-com.p.rapidapi.com';
    this.cache = new Map();
    this.cacheTTL = 30 * 60 * 1000; // 30 minutes cache TTL
  }

  /**
   * Makes an authenticated request to the weather API
   * 
   * @private
   * @param {string} endpoint - The API endpoint to call
   * @param {Object} [params={}] - Query parameters
   * @returns {Promise<Object>} The API response
   */
  async _makeRequest(endpoint, params = {}) {
    try {
      // Get API key from settings
      const settings = await this.api.settings.get();
      const apiKey = settings?.apiKey;

      if (!apiKey) {
        throw new Error('Weather API key not configured');
      }

      // Build URL with query parameters
      const queryString = new URLSearchParams(params).toString();
      const url = `${this.baseUrl}${endpoint}?${queryString}`;

      // Check cache first
      const cacheKey = `${endpoint}?${queryString}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp < this.cacheTTL)) {
        await this.logger.debug('Returning cached response for:', endpoint);
        return cached.data;
      }

      await this.logger.debug('Making API request to:', endpoint, params);

      // Make the request
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': this.rapidApiHost
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `API request failed with status ${response.status}: ${errorData.message || 'Unknown error'}`
        );
      }

      const data = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      await this.logger.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * Validates the API key by making a test request
   * 
   * @returns {Promise<boolean>} Whether the API key is valid
   */
  async validateAPIKey() {
    try {
      // Use a lightweight endpoint to validate the API key
      await this._makeRequest('/current.json', { q: '40.7128,-74.0060' });
      return true;
    } catch (error) {
      if (error.message.includes('API key') || error.message.includes('401')) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Fetches current weather data for a location
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @returns {Promise<Object>} Current weather data
   */
  async getCurrentWeather(lat, lon) {
    try {
      const data = await this._makeRequest('/current.json', {
        q: `${lat},${lon}`
      });

      return this._transformCurrentWeather(data);
    } catch (error) {
      await this.logger.error('Failed to fetch current weather:', error);
      throw error;
    }
  }

  /**
   * Fetches weather forecast for a location
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {number} [days=5] - Number of days to forecast (1-10)
   * @returns {Promise<Object>} Weather forecast data
   */
  async getForecast(lat, lon, days = 5) {
    try {
      const data = await this._makeRequest('/forecast.json', {
        q: `${lat},${lon}`,
        days: Math.min(Math.max(1, days), 10), // Ensure between 1-10 days
        aqi: 'yes',
        alerts: 'yes'
      });

      return this._transformForecast(data);
    } catch (error) {
      await this.logger.error('Failed to fetch forecast:', error);
      throw error;
    }
  }

  /**
   * Fetches both current weather and forecast for a location
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {number} [days=5] - Number of days to forecast (1-10)
   * @returns {Promise<Object>} Combined current and forecast data
   */
  async getWeatherData(lat, lon, days = 5) {
    try {
      const [current, forecast] = await Promise.all([
        this.getCurrentWeather(lat, lon),
        this.getForecast(lat, lon, days)
      ]);

      return {
        current,
        forecast,
        location: {
          name: current.location.name,
          region: current.location.region,
          country: current.location.country,
          lat: current.location.lat,
          lon: current.location.lon,
          tzId: current.location.tz_id,
          localtime: current.location.localtime
        },
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      await this.logger.error('Failed to fetch weather data:', error);
      throw error;
    }
  }

  /**
   * Transforms the current weather API response to a standardized format
   * 
   * @private
   * @param {Object} data - Raw API response
   * @returns {Object} Transformed current weather data
   */
  _transformCurrentWeather(data) {
    if (!data || !data.current || !data.location) {
      throw new Error('Invalid API response format');
    }

    const { current, location } = data;
    
    return {
      location: {
        name: location.name,
        region: location.region,
        country: location.country,
        lat: location.lat,
        lon: location.lon,
        tz_id: location.tz_id,
        localtime: location.localtime
      },
      current: {
        lastUpdated: current.last_updated,
        tempC: current.temp_c,
        tempF: current.temp_f,
        isDay: current.is_day === 1,
        condition: {
          text: current.condition?.text || 'Unknown',
          code: current.condition?.code || 1000,
          icon: current.condition?.icon || ''
        },
        windKph: current.wind_kph,
        windMph: current.wind_mph,
        windDegree: current.wind_degree,
        windDir: current.wind_dir,
        pressureMb: current.pressure_mb,
        pressureIn: current.pressure_in,
        precipMm: current.precip_mm,
        precipIn: current.precip_in,
        humidity: current.humidity,
        cloud: current.cloud,
        feelslikeC: current.feelslike_c,
        feelslikeF: current.feelslike_f,
        visKm: current.vis_km,
        visMiles: current.vis_miles,
        uv: current.uv,
        gustKph: current.gust_kph,
        gustMph: current.gust_mph,
        airQuality: current.air_quality ? {
          co: current.air_quality.co,
          no2: current.air_quality.no2,
          o3: current.air_quality.o3,
          so2: current.air_quality.so2,
          pm2_5: current.air_quality.pm2_5,
          pm10: current.air_quality.pm10,
          'us-epa-index': current.air_quality['us-epa-index'],
          'gb-defra-index': current.air_quality['gb-defra-index']
        } : null
      }
    };
  }

  /**
   * Transforms the forecast API response to a standardized format
   * 
   * @private
   * @param {Object} data - Raw API response
   * @returns {Object} Transformed forecast data
   */
  _transformForecast(data) {
    if (!data || !data.forecast || !data.forecast.forecastday) {
      throw new Error('Invalid forecast API response format');
    }

    const { forecast, location } = data;
    
    return {
      location: {
        name: location.name,
        region: location.region,
        country: location.country,
        lat: location.lat,
        lon: location.lon,
        tz_id: location.tz_id,
        localtime: location.localtime
      },
      forecast: forecast.forecastday.map(day => ({
        date: day.date,
        dateEpoch: day.date_epoch,
        day: {
          maxtempC: day.day.maxtemp_c,
          maxtempF: day.day.maxtemp_f,
          mintempC: day.day.mintemp_c,
          mintempF: day.day.mintemp_f,
          avgtempC: day.day.avgtemp_c,
          avgtempF: day.day.avgtemp_f,
          maxwindMph: day.day.maxwind_mph,
          maxwindKph: day.day.maxwind_kph,
          totalprecipMm: day.day.totalprecip_mm,
          totalprecipIn: day.day.totalprecip_in,
          totalsnowCm: day.day.totalsnow_cm || 0,
          avgvisKm: day.day.avgvis_km,
          avgvisMiles: day.day.avgvis_miles,
          avghumidity: day.day.avghumidity,
          dailyWillItRain: day.day.daily_will_it_rain === 1,
          dailyChanceOfRain: day.day.daily_chance_of_rain,
          dailyWillItSnow: day.day.daily_will_it_snow === 1,
          dailyChanceOfSnow: day.day.daily_chance_of_snow,
          condition: {
            text: day.day.condition?.text || 'Unknown',
            code: day.day.condition?.code || 1000,
            icon: day.day.condition?.icon || ''
          },
          uv: day.day.uv
        },
        astro: {
          sunrise: day.astro.sunrise,
          sunset: day.astro.sunset,
          moonrise: day.astro.moonrise,
          moonset: day.astro.moonset,
          moonPhase: day.astro.moon_phase,
          moonIllumination: day.astro.moon_illumination,
          isMoonUp: day.astro.is_moon_up === 1,
          isSunUp: day.astro.is_sun_up === 1
        },
        hour: day.hour.map(hour => ({
          timeEpoch: hour.time_epoch,
          time: hour.time,
          tempC: hour.temp_c,
          tempF: hour.temp_f,
          isDay: hour.is_day === 1,
          condition: {
            text: hour.condition?.text || 'Unknown',
            code: hour.condition?.code || 1000,
            icon: hour.condition?.icon || ''
          },
          windMph: hour.wind_mph,
          windKph: hour.wind_kph,
          windDegree: hour.wind_degree,
          windDir: hour.wind_dir,
          pressureMb: hour.pressure_mb,
          pressureIn: hour.pressure_in,
          precipMm: hour.precip_mm,
          precipIn: hour.precip_in,
          humidity: hour.humidity,
          cloud: hour.cloud,
          feelslikeC: hour.feelslike_c,
          feelslikeF: hour.feelslike_f,
          windchillC: hour.windchill_c,
          windchillF: hour.windchill_f,
          heatindexC: hour.heatindex_c,
          heatindexF: hour.heatindex_f,
          dewpointC: hour.dewpoint_c,
          dewpointF: hour.dewpoint_f,
          willItRain: hour.will_it_rain === 1,
          chanceOfRain: hour.chance_of_rain,
          willItSnow: hour.will_it_snow === 1,
          chanceOfSnow: hour.chance_of_snow,
          visKm: hour.vis_km,
          visMiles: hour.vis_miles,
          gustMph: hour.gust_mph,
          gustKph: hour.gust_kph,
          uv: hour.uv
        }))
      })),
      alerts: data.alerts?.alert?.map(alert => ({
        headline: alert.headline,
        msgType: alert.msgtype,
        severity: alert.severity,
        urgency: alert.urgency,
        areas: alert.areas,
        category: alert.category,
        certainty: alert.certainty,
        event: alert.event,
        note: alert.note,
        effective: alert.effective,
        expires: alert.expires,
        desc: alert.desc,
        instruction: alert.instruction
      })) || []
    };
  }
}

module.exports = WeatherAPI;
