/**
 * Unit Converter for Weather Plugin
 * 
 * Handles conversion between metric and imperial units for weather data.
 * Provides utility functions for temperature, speed, distance, and pressure.
 * 
 * @module UnitConverter
 */

/**
 * Unit Converter class for handling weather unit conversions
 * 
 * @class UnitConverter
 */
class UnitConverter {
  /**
   * Creates a new UnitConverter instance
   * 
   * @param {Object} logger - The logger instance
   */
  constructor(logger) {
    this.logger = logger;
    
    // Conversion factors and constants
    this.CONVERSION = {
      // Temperature
      C_TO_F: (c) => (c * 9/5) + 32,
      F_TO_C: (f) => (f - 32) * 5/9,
      
      // Length/Distance
      KM_TO_MI: 0.621371,
      MI_TO_KM: 1.60934,
      MM_TO_IN: 0.0393701,
      IN_TO_MM: 25.4,
      CM_TO_IN: 0.393701,
      IN_TO_CM: 2.54,
      
      // Speed
      KMH_TO_MPH: 0.621371,
      MPH_TO_KMH: 1.60934,
      
      // Pressure
      MB_TO_INHG: 0.02953,
      INHG_TO_MB: 33.8639,
      
      // Precipitation
      MM_TO_INCH: 0.0393701,
      INCH_TO_MM: 25.4
    };
    
    // Unit labels
    this.UNITS = {
      metric: {
        temperature: '°C',
        speed: 'km/h',
        distance: 'km',
        precipitation: 'mm',
        pressure: 'mb',
        visibility: 'km'
      },
      imperial: {
        temperature: '°F',
        speed: 'mph',
        distance: 'mi',
        precipitation: 'in',
        pressure: 'inHg',
        visibility: 'mi'
      }
    };
  }

  /**
   * Converts temperature between Celsius and Fahrenheit
   * 
   * @param {number} value - Temperature value to convert
   * @param {string} from - Source unit ('c' or 'f')
   * @param {string} to - Target unit ('c' or 'f')
   * @param {number} [decimals=1] - Number of decimal places to round to
   * @returns {number} Converted temperature
   */
  convertTemperature(value, from, to, decimals = 1) {
    if (from === to) return this._round(value, decimals);
    
    try {
      let result;
      
      if (from === 'c' && to === 'f') {
        result = this.CONVERSION.C_TO_F(value);
      } else if (from === 'f' && to === 'c') {
        result = this.CONVERSION.F_TO_C(value);
      } else {
        throw new Error(`Unsupported temperature conversion: ${from} to ${to}`);
      }
      
      return this._round(result, decimals);
    } catch (error) {
      this.logger.error('Temperature conversion error:', error);
      return value; // Return original value on error
    }
  }

  /**
   * Converts speed between kilometers per hour and miles per hour
   * 
   * @param {number} value - Speed value to convert
   * @param {string} from - Source unit ('kmh' or 'mph')
   * @param {string} to - Target unit ('kmh' or 'mph')
   * @param {number} [decimals=1] - Number of decimal places to round to
   * @returns {number} Converted speed
   */
  convertSpeed(value, from, to, decimals = 1) {
    if (from === to) return this._round(value, decimals);
    
    try {
      let result;
      
      if (from === 'kmh' && to === 'mph') {
        result = value * this.CONVERSION.KMH_TO_MPH;
      } else if (from === 'mph' && to === 'kmh') {
        result = value * this.CONVERSION.MPH_TO_KMH;
      } else {
        throw new Error(`Unsupported speed conversion: ${from} to ${to}`);
      }
      
      return this._round(result, decimals);
    } catch (error) {
      this.logger.error('Speed conversion error:', error);
      return value; // Return original value on error
    }
  }

  /**
   * Converts distance between kilometers and miles
   * 
   * @param {number} value - Distance value to convert
   * @param {string} from - Source unit ('km' or 'mi')
   * @param {string} to - Target unit ('km' or 'mi')
   * @param {number} [decimals=1] - Number of decimal places to round to
   * @returns {number} Converted distance
   */
  convertDistance(value, from, to, decimals = 1) {
    if (from === to) return this._round(value, decimals);
    
    try {
      let result;
      
      if (from === 'km' && to === 'mi') {
        result = value * this.CONVERSION.KM_TO_MI;
      } else if (from === 'mi' && to === 'km') {
        result = value * this.CONVERSION.MI_TO_KM;
      } else {
        throw new Error(`Unsupported distance conversion: ${from} to ${to}`);
      }
      
      return this._round(result, decimals);
    } catch (error) {
      this.logger.error('Distance conversion error:', error);
      return value; // Return original value on error
    }
  }

  /**
   * Converts precipitation between millimeters and inches
   * 
   * @param {number} value - Precipitation value to convert
   * @param {string} from - Source unit ('mm' or 'in')
   * @param {string} to - Target unit ('mm' or 'in')
   * @param {number} [decimals=1] - Number of decimal places to round to
   * @returns {number} Converted precipitation
   */
  convertPrecipitation(value, from, to, decimals = 1) {
    if (from === to) return this._round(value, decimals);
    
    try {
      let result;
      
      if (from === 'mm' && to === 'in') {
        result = value * this.CONVERSION.MM_TO_INCH;
      } else if (from === 'in' && to === 'mm') {
        result = value * this.CONVERSION.INCH_TO_MM;
      } else {
        throw new Error(`Unsupported precipitation conversion: ${from} to ${to}`);
      }
      
      return this._round(result, decimals);
    } catch (error) {
      this.logger.error('Precipitation conversion error:', error);
      return value; // Return original value on error
    }
  }

  /**
   * Converts pressure between millibars and inches of mercury
   * 
   * @param {number} value - Pressure value to convert
   * @param {string} from - Source unit ('mb' or 'inHg')
   * @param {string} to - Target unit ('mb' or 'inHg')
   * @param {number} [decimals=1] - Number of decimal places to round to
   * @returns {number} Converted pressure
   */
  convertPressure(value, from, to, decimals = 1) {
    if (from === to) return this._round(value, decimals);
    
    try {
      let result;
      
      if (from === 'mb' && to === 'inHg') {
        result = value * this.CONVERSION.MB_TO_INHG;
      } else if (from === 'inHg' && to === 'mb') {
        result = value * this.CONVERSION.INHG_TO_MB;
      } else {
        throw new Error(`Unsupported pressure conversion: ${from} to ${to}`);
      }
      
      return this._round(result, decimals);
    } catch (error) {
      this.logger.error('Pressure conversion error:', error);
      return value; // Return original value on error
    }
  }

  /**
   * Converts a weather data object from one unit system to another
   * 
   * @param {Object} data - Weather data to convert
   * @param {string} from - Source unit system ('metric' or 'imperial')
   * @param {string} to - Target unit system ('metric' or 'imperial')
   * @returns {Object} Converted weather data
   */
  convertWeatherData(data, from, to) {
    if (from === to || !data) return data;
    
    try {
      // Create a deep copy to avoid modifying the original
      const converted = JSON.parse(JSON.stringify(data));
      
      // Convert current weather
      if (converted.current) {
        converted.current = this._convertCurrentWeather(converted.current, from, to);
      }
      
      // Convert forecast
      if (converted.forecast && Array.isArray(converted.forecast)) {
        converted.forecast = converted.forecast.map(day => ({
          ...day,
          ...this._convertDayForecast(day, from, to)
        }));
      }
      
      // Convert units in the root if present
      if (converted.units) {
        converted.units = to;
      }
      
      return converted;
    } catch (error) {
      this.logger.error('Weather data conversion error:', error);
      return data; // Return original data on error
    }
  }

  /**
   * Converts current weather data between unit systems
   * 
   * @private
   * @param {Object} current - Current weather data
   * @param {string} from - Source unit system ('metric' or 'imperial')
   * @param {string} to - Target unit system ('metric' or 'imperial')
   * @returns {Object} Converted current weather data
   */
  _convertCurrentWeather(current, from, to) {
    if (!current) return current;
    
    const converted = { ...current };
    
    // Temperature fields
    const tempFields = [
      'temp_c', 'temp_f', 'feelslike_c', 'feelslike_f',
      'dewpoint_c', 'dewpoint_f', 'heatindex_c', 'heatindex_f',
      'windchill_c', 'windchill_f', 'avgtemp_c', 'avgtemp_f',
      'maxtemp_c', 'maxtemp_f', 'mintemp_c', 'mintemp_f',
      'avgtemp_c', 'avgtemp_f'
    ];
    
    // Convert temperature fields
    for (const field of tempFields) {
      if (field in converted) {
        const [base, unit] = field.split('_');
        if (unit === 'c' && to === 'imperial') {
          converted[`${base}_f`] = this.convertTemperature(converted[field], 'c', 'f');
        } else if (unit === 'f' && to === 'metric') {
          converted[`${base}_c`] = this.convertTemperature(converted[field], 'f', 'c');
        }
      }
    }
    
    // Convert wind speed
    if (converted.wind_kph !== undefined && converted.wind_mph !== undefined) {
      if (to === 'imperial') {
        converted.wind_mph = this.convertSpeed(converted.wind_kph, 'kmh', 'mph');
      } else {
        converted.wind_kph = this.convertSpeed(converted.wind_mph, 'mph', 'kmh');
      }
    }
    
    // Convert visibility
    if (converted.vis_km !== undefined && converted.vis_miles !== undefined) {
      if (to === 'imperial') {
        converted.vis_miles = this.convertDistance(converted.vis_km, 'km', 'mi');
      } else {
        converted.vis_km = this.convertDistance(converted.vis_miles, 'mi', 'km');
      }
    }
    
    // Convert pressure
    if (converted.pressure_mb !== undefined && converted.pressure_in !== undefined) {
      if (to === 'imperial') {
        converted.pressure_in = this.convertPressure(converted.pressure_mb, 'mb', 'inHg');
      } else {
        converted.pressure_mb = this.convertPressure(converted.pressure_in, 'inHg', 'mb');
      }
    }
    
    // Convert precipitation
    if (converted.precip_mm !== undefined && converted.precip_in !== undefined) {
      if (to === 'imperial') {
        converted.precip_in = this.convertPrecipitation(converted.precip_mm, 'mm', 'in');
      } else {
        converted.precip_mm = this.convertPrecipitation(converted.precip_in, 'in', 'mm');
      }
    }
    
    // Convert air quality if present
    if (converted.air_quality) {
      // Some air quality values might need conversion in the future
    }
    
    return converted;
  }

  /**
   * Converts daily forecast data between unit systems
   * 
   * @private
   * @param {Object} day - Daily forecast data
   * @param {string} from - Source unit system ('metric' or 'imperial')
   * @param {string} to - Target unit system ('metric' or 'imperial')
   * @returns {Object} Converted daily forecast data
   */
  _convertDayForecast(day, from, to) {
    if (!day) return day;
    
    const converted = { ...day };
    
    // Convert day data
    if (day.day) {
      converted.day = { ...day.day };
      
      // Temperature fields in day forecast
      const tempFields = [
        'maxtemp_c', 'maxtemp_f', 'mintemp_c', 'mintemp_f',
        'avgtemp_c', 'avgtemp_f', 'maxwind_kph', 'maxwind_mph',
        'totalprecip_mm', 'totalprecip_in', 'avgvis_km', 'avgvis_miles'
      ];
      
      for (const field of tempFields) {
        if (field in converted.day) {
          const [base, unit] = field.split('_');
          
          if (unit === 'c' && to === 'imperial') {
            converted.day[`${base}_f`] = this.convertTemperature(converted.day[field], 'c', 'f');
          } else if (unit === 'f' && to === 'metric') {
            converted.day[`${base}_c`] = this.convertTemperature(converted.day[field], 'f', 'c');
          } else if (field === 'maxwind_kph' && to === 'imperial') {
            converted.day.maxwind_mph = this.convertSpeed(converted.day[field], 'kmh', 'mph');
          } else if (field === 'maxwind_mph' && to === 'metric') {
            converted.day.maxwind_kph = this.convertSpeed(converted.day[field], 'mph', 'kmh');
          } else if (field === 'totalprecip_mm' && to === 'imperial') {
            converted.day.totalprecip_in = this.convertPrecipitation(converted.day[field], 'mm', 'in');
          } else if (field === 'totalprecip_in' && to === 'metric') {
            converted.day.totalprecip_mm = this.convertPrecipitation(converted.day[field], 'in', 'mm');
          } else if (field === 'avgvis_km' && to === 'imperial') {
            converted.day.avgvis_miles = this.convertDistance(converted.day[field], 'km', 'mi');
          } else if (field === 'avgvis_miles' && to === 'metric') {
            converted.day.avgvis_km = this.convertDistance(converted.day[field], 'mi', 'km');
          }
        }
      }
    }
    
    // Convert hourly data if present
    if (Array.isArray(day.hour)) {
      converted.hour = day.hour.map(hour => this._convertCurrentWeather(hour, from, to));
    }
    
    return converted;
  }

  /**
   * Gets the unit symbol for a given measurement type
   * 
   * @param {string} type - Measurement type ('temperature', 'speed', 'distance', etc.)
   * @param {string} [system='metric'] - Unit system ('metric' or 'imperial')
   * @returns {string} Unit symbol
   */
  getUnitSymbol(type, system = 'metric') {
    return this.UNITS[system]?.[type] || '';
  }

  /**
   * Rounds a number to the specified number of decimal places
   * 
   * @private
   * @param {number} value - Number to round
   * @param {number} decimals - Number of decimal places
   * @returns {number} Rounded number
   */
  _round(value, decimals) {
    if (value === undefined || value === null) return value;
    const factor = Math.pow(10, decimals);
    return Math.round((value + Number.EPSILON) * factor) / factor;
  }
}

module.exports = UnitConverter;
