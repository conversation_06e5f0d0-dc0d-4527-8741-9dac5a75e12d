#!/bin/bash

# Exit on any error
set -e

# Set the path to the tests directory
TESTS_DIR="./tests"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
  echo "Error: npm is not installed. Please install Node.js and npm first."
  exit 1
fi

# Install test dependencies locally if they're not already installed
if [ ! -d "node_modules/mocha" ] || [ ! -d "node_modules/chai" ] || [ ! -d "node_modules/sinon" ] || [ ! -d "node_modules/nock" ]; then
  echo "Installing test dependencies locally..."
  npm install --no-package-lock --no-save mocha chai sinon nock node-fetch@2
fi

# Function to run a specific test file
run_test() {
  local test_file=$1
  echo "Running $test_file..."
  ./node_modules/.bin/mocha --timeout 10000 --exit "$test_file"
  echo ""
}

# Run all test files if no specific test is provided
if [ $# -eq 0 ]; then
  echo "Running all tests..."
  echo "===================="
  
  # Run unit tests
  echo "UNIT TESTS"
  echo "----------"
  for test_file in "$TESTS_DIR"/*.test.js; do
    if [ -f "$test_file" ]; then
      run_test "$test_file"
    fi
  done
  
  # Run integration tests
  echo "INTEGRATION TESTS"
  echo "-----------------"
  if [ -f "$TESTS_DIR/integration.test.js" ]; then
    run_test "$TESTS_DIR/integration.test.js"
  else
    echo "No integration tests found."
  fi
  
  echo "All tests completed successfully!"
  exit 0
fi

# Run a specific test file if provided
if [ -f "$1" ]; then
  run_test "$1"
  exit 0
elif [ -f "$TESTS_DIR/$1" ]; then
  run_test "$TESTS_DIR/$1"
  exit 0
else
  echo "Error: Test file not found: $1"
  echo "Available test files:"
  find "$TESTS_DIR" -name "*.test.js" | sed 's/^/  /'
  exit 1
fi
