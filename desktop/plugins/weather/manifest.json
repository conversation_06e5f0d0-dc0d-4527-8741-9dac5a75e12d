{"author": "Lifeboard Team", "description": "Weather forecast integration with 5-day forecasts and real-time weather data", "homepage": "https://lifeboard.app/plugins/weather", "id": "weather", "main": "main.js", "minAppVersion": "0.1.0", "name": "Weather Plugin", "permissions": ["network", "storage"], "settings": {"apiKey": {"description": "RapidAPI Weather API Key", "sensitive": true, "type": "string"}, "latitude": {"description": "Latitude for weather forecast (e.g., 37.7749)", "type": "number"}, "longitude": {"description": "Longitude for weather forecast (e.g., -122.4194)", "type": "number"}, "units": {"default": "metric", "description": "Units for temperature and measurements", "enum": ["metric", "imperial"], "type": "string"}, "autoSync": {"default": true, "description": "Enable automatic weather data synchronization", "type": "boolean"}, "syncInterval": {"default": 43200, "description": "Sync interval in seconds (default: 12 hours)", "type": "number"}}, "version": "1.0.0"}