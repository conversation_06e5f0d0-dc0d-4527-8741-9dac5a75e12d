/**
 * PluginRegistry.js
 * Manages global plugin registry with metadata and preferences
 *
 * Handles global plugin registry at $APPDATA/lifeboard/plugins/global/registry.json
 */

const fs = require('fs');
const path = require('path');
const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * PluginRegistry manages global plugin metadata and preferences
 * Manages registry file located in $APPDATA/lifeboard/plugins/global/registry.json
 */
class PluginRegistry {
  constructor(baseDirectory, logDir) {
    this.baseDir = baseDirectory;
    this.globalDir = path.join(baseDirectory, 'global');
    this.registryPath = path.join(this.globalDir, 'registry.json');
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'PluginRegistry', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('PluginRegistry');
    }
    this.preferencesPath = path.join(this.globalDir, 'preferences.json');
    this.registry = new Map();
    this.preferences = {};
    this.initializeLogger();
    this.initializeDirectories();

    this.this.log.INFO('[PluginRegistry] PluginRegistry initialized');
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    this.this.log.INFO('[PluginRegistry] Logger initialized');
  }

  /**
   * Initialize global directories
   * @private
   */
  initializeDirectories() {
    // Ensure global directory exists
    if (!fs.existsSync(this.globalDir)) {
      fs.mkdirSync(this.globalDir, { recursive: true });
      this.log.INFO(`[PluginRegistry] Created global directory: ${this.globalDir}`);
    }

    // Load existing registry
    this.loadRegistry();
    this.loadPreferences();
  }

  /**
   * Create default registry structure
   * @returns {object} Default registry object
   * @private
   */
  createDefaultRegistry() {
    return {
      version: '1.0.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      plugins: {}
    };
  }

  /**
   * Create default preferences structure
   * @returns {object} Default preferences object
   * @private
   */
  createDefaultPreferences() {
    return {
      version: '1.0.0',
      autoEnableOnInstall: true,
      showNotifications: true,
      maxPluginsEnabled: 50,
      checkUpdatesInterval: 3600000, // 1 hour in milliseconds
      allowBetaPlugins: false,
      enableLogging: true,
      created: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };
  }

  /**
   * Load registry from file
   * @private
   */
  loadRegistry() {
    this.log.DEBUG('[PluginRegistry] Loading plugin registry');

    try {
      let registryData = null;

      if (fs.existsSync(this.registryPath)) {
        try {
          const fileContent = fs.readFileSync(this.registryPath, 'utf8');
          this.log.DEBUG(`[PluginRegistry] Read registry file: ${this.registryPath}`);

          if (fileContent.trim()) {
            registryData = JSON.parse(fileContent);
            this.log.DEBUG('[PluginRegistry] Parsed registry data');
          } else {
            this.log.WARN(`[PluginRegistry] Registry file is empty: ${this.registryPath}`);
          }
        } catch (parseError) {
          this.log.ERROR(`[PluginRegistry] Failed to parse registry file ${this.registryPath}:`, parseError);
          throw new Error(`Malformed JSON in registry file: ${parseError.message}`);
        }
      }

      // Use default if no registry found
      if (!registryData) {
        this.log.INFO('[PluginRegistry] No registry file found, creating default');
        registryData = this.createDefaultRegistry();
        this.saveRegistry();
      }

      // Convert plugins object to Map for easier management
      this.registry.clear();
      if (registryData.plugins) {
        Object.entries(registryData.plugins).forEach(([id, plugin]) => {
          this.registry.set(id, plugin);
        });
      }

      this.log.INFO(`[PluginRegistry] Loaded registry with ${this.registry.size} plugins`);
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to load registry:', error);
      throw error;
    }
  }

  /**
   * Save registry to file
   * @private
   */
  saveRegistry() {
    this.log.DEBUG('[PluginRegistry] Saving plugin registry');

    try {
      // Convert Map back to object for serialization
      const pluginsObject = {};
      this.registry.forEach((plugin, id) => {
        pluginsObject[id] = plugin;
      });

      const registryData = {
        version: '1.0.0',
        created: this.created || new Date().toISOString(),
        lastModified: new Date().toISOString(),
        plugins: pluginsObject
      };

      const jsonString = JSON.stringify(registryData, null, 2);
      fs.writeFileSync(this.registryPath, jsonString, 'utf8');

      this.log.INFO('[PluginRegistry] Successfully saved registry');
      this.log.DEBUG(`[PluginRegistry] Registry saved with ${this.registry.size} plugins`);
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to save registry:', error);
      throw error;
    }
  }

  /**
   * Load preferences from file
   * @private
   */
  loadPreferences() {
    this.log.DEBUG('[PluginRegistry] Loading preferences');

    try {
      if (fs.existsSync(this.preferencesPath)) {
        try {
          const fileContent = fs.readFileSync(this.preferencesPath, 'utf8');
          this.log.DEBUG(`[PluginRegistry] Read preferences file: ${this.preferencesPath}`);

          if (fileContent.trim()) {
            this.preferences = JSON.parse(fileContent);
            this.log.DEBUG('[PluginRegistry] Parsed preferences data');
          } else {
            this.log.WARN(`[PluginRegistry] Preferences file is empty: ${this.preferencesPath}`);
            this.preferences = this.createDefaultPreferences();
          }
        } catch (parseError) {
          this.log.ERROR(`[PluginRegistry] Failed to parse preferences file ${this.preferencesPath}:`, parseError);
          this.preferences = this.createDefaultPreferences();
        }
      } else {
        this.log.INFO('[PluginRegistry] No preferences file found, creating default');
        this.preferences = this.createDefaultPreferences();
        this.savePreferences();
      }

      this.log.INFO('[PluginRegistry] Loaded preferences');
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to load preferences:', error);
      this.preferences = this.createDefaultPreferences();
    }
  }

  /**
   * Save preferences to file
   * @private
   */
  savePreferences() {
    this.log.DEBUG('[PluginRegistry] Saving preferences');

    try {
      const preferencesData = {
        ...this.preferences,
        lastModified: new Date().toISOString()
      };

      const jsonString = JSON.stringify(preferencesData, null, 2);
      fs.writeFileSync(this.preferencesPath, jsonString, 'utf8');

      this.log.INFO('[PluginRegistry] Successfully saved preferences');
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to save preferences:', error);
      throw error;
    }
  }

  /**
   * Register a plugin in the global registry
   * @param {object} pluginInfo - Plugin information
   * @returns {boolean} True if registered successfully
   */
  registerPlugin(pluginInfo) {
    this.log.INFO(`[PluginRegistry] Registering plugin: ${pluginInfo.id}`);

    try {
      if (!pluginInfo || !pluginInfo.id) {
        throw new Error('Plugin info must include id field');
      }

      const requiredFields = ['id', 'name', 'version'];
      const missingFields = requiredFields.filter(field => !pluginInfo[field]);
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      const registryEntry = {
        id: pluginInfo.id,
        name: pluginInfo.name,
        version: pluginInfo.version,
        state: pluginInfo.state || 'discovered',
        installDate: pluginInfo.installDate || new Date().toISOString(),
        source: pluginInfo.source || 'local',
        manifest: {
          permissions: pluginInfo.permissions || [],
          minAppVersion: pluginInfo.minAppVersion || '0.1.0',
          ...pluginInfo.manifest
        },
        metadata: {
          lastActivity: null,
          loadCount: 0,
          errorCount: 0,
          ...pluginInfo.metadata
        }
      };

      this.registry.set(pluginInfo.id, registryEntry);
      this.saveRegistry();

      this.log.INFO(`[PluginRegistry] Successfully registered plugin: ${pluginInfo.id}`);
      return true;
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Failed to register plugin ${pluginInfo?.id}:`, error);
      return false;
    }
  }



  /**
   * Get all registered plugins
   * @returns {Array} Array of all registered plugins
   */
  getAllPlugins() {
    this.log.DEBUG('[PluginRegistry] Getting all registered plugins');

    const plugins = Array.from(this.registry.values());
    this.log.DEBUG(`[PluginRegistry] Retrieved ${plugins.length} registered plugins`);
    return plugins;
  }

  /**
   * Get a specific plugin by ID
   * @param {string} pluginId - The plugin identifier
   * @returns {object|null} Plugin info or null if not found
   */
  getPlugin(pluginId) {
    this.log.DEBUG(`[PluginRegistry] Getting plugin: ${pluginId}`);

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      const plugin = this.registry.get(pluginId);
      if (plugin) {
        this.log.DEBUG(`[PluginRegistry] Found plugin: ${pluginId}`);
        return plugin;
      } else {
        this.log.DEBUG(`[PluginRegistry] Plugin not found: ${pluginId}`);
        return null;
      }
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Failed to get plugin ${pluginId}:`, error);
      return null;
    }
  }

  /**
   * Get plugins by state
   * @param {string} state - The plugin state to filter by
   * @returns {Array} Array of plugins with the specified state
   */
  getPluginsByState(state) {
    this.log.DEBUG(`[PluginRegistry] Getting plugins by state: ${state}`);

    try {
      if (!state || typeof state !== 'string') {
        throw new Error('State must be a non-empty string');
      }

      const plugins = Array.from(this.registry.values()).filter(plugin => plugin.state === state);
      this.log.DEBUG(`[PluginRegistry] Found ${plugins.length} plugins with state: ${state}`);
      return plugins;
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Failed to get plugins by state ${state}:`, error);
      return [];
    }
  }

  /**
   * Update plugin state in registry
   * @param {string} pluginId - The plugin identifier
   * @param {string} newState - The new state
   * @returns {boolean} True if updated successfully
   */
  updatePluginState(pluginId, newState) {
    this.log.DEBUG(`[PluginRegistry] Updating plugin state: ${pluginId} -> ${newState}`);

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      if (!newState || typeof newState !== 'string') {
        throw new Error('New state must be a non-empty string');
      }

      const plugin = this.registry.get(pluginId);
      if (!plugin) {
        this.log.WARN(`[PluginRegistry] Plugin ${pluginId} not found in registry`);
        return false;
      }

      plugin.state = newState;
      plugin.lastModified = new Date().toISOString();

      this.registry.set(pluginId, plugin);
      this.saveRegistry();

      this.log.INFO(`[PluginRegistry] Updated plugin ${pluginId} state to: ${newState}`);
      return true;
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Failed to update plugin state ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Update plugin metadata
   * @param {string} pluginId - The plugin identifier
   * @param {object} metadata - Metadata to update
   * @returns {boolean} True if updated successfully
   */
  updatePluginMetadata(pluginId, metadata) {
    this.log.DEBUG(`[PluginRegistry] Updating plugin metadata: ${pluginId}`);

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      if (!metadata || typeof metadata !== 'object') {
        throw new Error('Metadata must be an object');
      }

      const plugin = this.registry.get(pluginId);
      if (!plugin) {
        this.log.WARN(`[PluginRegistry] Plugin ${pluginId} not found in registry`);
        return false;
      }

      plugin.metadata = {
        ...plugin.metadata,
        ...metadata,
        lastActivity: new Date().toISOString()
      };

      this.registry.set(pluginId, plugin);
      this.saveRegistry();

      this.log.INFO(`[PluginRegistry] Updated metadata for plugin: ${pluginId}`);
      return true;
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Failed to update plugin metadata ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Get current preferences
   * @returns {object} Current preferences
   */
  getPreferences() {
    this.log.DEBUG('[PluginRegistry] Getting preferences');
    return { ...this.preferences };
  }

  /**
   * Update preferences
   * @param {object} newPreferences - Preferences to update
   * @returns {boolean} True if updated successfully
   */
  updatePreferences(newPreferences) {
    this.log.INFO('[PluginRegistry] Updating preferences');

    try {
      if (!newPreferences || typeof newPreferences !== 'object') {
        throw new Error('Preferences must be an object');
      }

      this.preferences = {
        ...this.preferences,
        ...newPreferences,
        lastModified: new Date().toISOString()
      };

      this.savePreferences();

      this.log.INFO('[PluginRegistry] Successfully updated preferences');
      return true;
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to update preferences:', error);
      return false;
    }
  }

  /**
   * Get registry statistics
   * @returns {object} Registry statistics
   */
  getStatistics() {
    this.log.DEBUG('[PluginRegistry] Getting registry statistics');

    try {
      const totalPlugins = this.registry.size;
      const pluginsByState = {};

      // Count plugins by state
      this.registry.forEach(plugin => {
        const state = plugin.state || 'unknown';
        pluginsByState[state] = (pluginsByState[state] || 0) + 1;
      });

      const stats = {
        totalPlugins,
        pluginsByState,
        registrySize: this.registry.size,
        lastModified: new Date().toISOString()
      };

      this.log.DEBUG(`[PluginRegistry] Generated statistics: ${totalPlugins} total plugins`);
      return stats;
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to get statistics:', error);
      return {
        totalPlugins: 0,
        pluginsByState: {},
        registrySize: 0,
        error: error.message
      };
    }
  }

  /**
   * Check if a plugin is registered
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if plugin is registered
   */
  hasPlugin(pluginId) {
    try {
      return this.registry.has(pluginId);
    } catch (error) {
      this.log.ERROR(`[PluginRegistry] Error checking plugin existence ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Clear all plugins from registry (dangerous operation)
   * @returns {boolean} True if cleared successfully
   */
  clearRegistry() {
    this.log.WARN('[PluginRegistry] Clearing entire registry - this is a destructive operation');

    try {
      this.registry.clear();
      this.saveRegistry();

      this.log.WARN('[PluginRegistry] Registry cleared successfully');
      return true;
    } catch (error) {
      this.log.ERROR('[PluginRegistry] Failed to clear registry:', error);
      return false;
    }
  }
}

module.exports = PluginRegistry;
