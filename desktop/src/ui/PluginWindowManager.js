const { BrowserWindow, dialog } = require('electron');
const path = require('path');
const CoreLogger = require('../../core/logger/CoreLogger');
const { v4: uuidv4 } = require('uuid');

class PluginWindowManager {
    constructor() {
        this.openWindows = new Map();
        this.logger = CoreLogger.getInstance();
    }

    /**
     * Show a plugin settings window
     * @param {string} pluginId - The ID of the plugin
     * @param {string} htmlPath - Path to the HTML file for the plugin settings
     * @param {Object} options - Window options
     * @returns {Promise<BrowserWindow>} The created window
     */
    async showPluginSettingsWindow(pluginId, htmlPath, options = {}) {
        const correlationId = uuidv4();
        
        this.logger.info('Creating plugin settings window', {
            pluginId,
            htmlPath,
            correlationId,
            component: 'PluginWindowManager'
        });

        // Check if window already exists for this plugin
        if (this.openWindows.has(pluginId)) {
            const existingWindow = this.openWindows.get(pluginId);
            if (!existingWindow.isDestroyed()) {
                existingWindow.focus();
                this.logger.info('Focused existing plugin window', {
                    pluginId,
                    correlationId,
                    component: 'PluginWindowManager'
                });
                return existingWindow;
            } else {
                this.openWindows.delete(pluginId);
            }
        }

        const windowOptions = {
            width: options.width || 800,
            height: options.height || 600,
            modal: options.modal !== false,
            parent: options.parent || null,
            resizable: options.resizable !== false,
            minimizable: false,
            maximizable: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, '..', 'preload-plugin.js'),
                additionalArguments: [`--plugin-id=${pluginId}`, `--correlation-id=${correlationId}`]
            },
            show: false,
            title: options.title || `${pluginId} Settings`,
            ...options
        };

        const window = new BrowserWindow(windowOptions);
        
        // Store window reference
        this.openWindows.set(pluginId, window);

        // Set up event handlers
        this.setupWindowEventHandlers(window, pluginId, correlationId);

        // Load the HTML file
        try {
            await window.loadFile(htmlPath);
            window.show();
            
            this.logger.info('Plugin settings window created successfully', {
                pluginId,
                htmlPath,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
            
            return window;
        } catch (error) {
            this.logger.error('Failed to load plugin settings window', {
                pluginId,
                htmlPath,
                correlationId,
                error: error.message,
                component: 'PluginWindowManager'
            });
            
            window.destroy();
            this.openWindows.delete(pluginId);
            throw error;
        }
    }

    /**
     * Close a plugin window
     * @param {string} pluginId - The ID of the plugin
     */
    closePluginWindow(pluginId) {
        const correlationId = uuidv4();
        
        this.logger.info('Closing plugin window', {
            pluginId,
            correlationId,
            component: 'PluginWindowManager'
        });

        const window = this.openWindows.get(pluginId);
        if (window && !window.isDestroyed()) {
            window.close();
        }
        this.openWindows.delete(pluginId);
    }

    /**
     * Get all open plugin windows
     * @returns {Map<string, BrowserWindow>} Map of plugin IDs to windows
     */
    getOpenWindows() {
        // Clean up destroyed windows
        for (const [pluginId, window] of this.openWindows.entries()) {
            if (window.isDestroyed()) {
                this.openWindows.delete(pluginId);
            }
        }
        return new Map(this.openWindows);
    }

    /**
     * Focus a plugin window if it exists
     * @param {string} pluginId - The ID of the plugin
     * @returns {boolean} True if window was focused, false if not found
     */
    focusPluginWindow(pluginId) {
        const window = this.openWindows.get(pluginId);
        if (window && !window.isDestroyed()) {
            window.focus();
            return true;
        }
        return false;
    }

    /**
     * Set up event handlers for a plugin window
     * @param {BrowserWindow} window - The window to set up handlers for
     * @param {string} pluginId - The plugin ID
     * @param {string} correlationId - The correlation ID
     */
    setupWindowEventHandlers(window, pluginId, correlationId) {
        window.on('ready-to-show', () => {
            this.logger.info('Plugin window ready to show', {
                pluginId,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
        });

        window.on('closed', () => {
            this.logger.info('Plugin window closed', {
                pluginId,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
            this.openWindows.delete(pluginId);
        });

        window.on('focus', () => {
            this.logger.debug('Plugin window focused', {
                pluginId,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
        });

        window.on('blur', () => {
            this.logger.debug('Plugin window blurred', {
                pluginId,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
        });

        window.webContents.on('did-finish-load', () => {
            this.logger.info('Plugin window content loaded', {
                pluginId,
                correlationId,
                windowId: window.id,
                component: 'PluginWindowManager'
            });
        });

        window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            this.logger.error('Plugin window content failed to load', {
                pluginId,
                correlationId,
                windowId: window.id,
                errorCode,
                errorDescription,
                component: 'PluginWindowManager'
            });
        });

        window.webContents.on('console-message', (event, level, message, line, sourceId) => {
            this.logger.debug('Plugin window console message', {
                pluginId,
                correlationId,
                windowId: window.id,
                level,
                message,
                line,
                sourceId,
                component: 'PluginWindowManager'
            });
        });
    }

    /**
     * Show an error dialog
     * @param {string} title - Error title
     * @param {string} message - Error message
     * @param {BrowserWindow} parentWindow - Parent window
     */
    showErrorDialog(title, message, parentWindow = null) {
        const correlationId = uuidv4();
        
        this.logger.error('Showing error dialog', {
            title,
            message,
            correlationId,
            component: 'PluginWindowManager'
        });

        dialog.showErrorBox(title, message);
    }

    /**
     * Cleanup all plugin windows
     */
    cleanup() {
        const correlationId = uuidv4();
        
        this.logger.info('Cleaning up all plugin windows', {
            correlationId,
            openWindowCount: this.openWindows.size,
            component: 'PluginWindowManager'
        });

        for (const [pluginId, window] of this.openWindows.entries()) {
            if (!window.isDestroyed()) {
                window.close();
            }
        }
        this.openWindows.clear();
    }
}

module.exports = PluginWindowManager;