const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const { v4: uuidv4 } = require('uuid');

// Extract plugin ID and correlation ID from command line arguments
const pluginId = process.argv.find(arg => arg.startsWith('--plugin-id='))?.split('=')[1];
const initialCorrelationId = process.argv.find(arg => arg.startsWith('--correlation-id='))?.split('=')[1];

/**
 * Generate a new correlation ID for tracking operations
 * @returns {string} New correlation ID
 */
function generateCorrelationId() {
    return uuidv4();
}

/**
 * Log user actions with correlation tracking
 * @param {string} action - The action performed
 * @param {Object} context - Additional context
 */
function logUserAction(action, context = {}) {
    const correlationId = generateCorrelationId();
    ipcRenderer.send('log-user-action', {
        pluginId,
        action,
        context,
        correlationId,
        timestamp: new Date().toISOString()
    });
}

/**
 * Settings operations API
 */
const settingsAPI = {
    /**
     * Save plugin settings
     * @param {Object} settings - Settings to save
     * @returns {Promise<Object>} Save result
     */
    async save(settings) {
        const correlationId = generateCorrelationId();
        
        logUserAction('settings_save_attempt', {
            settingsKeys: Object.keys(settings),
            correlationId
        });

        try {
            const result = await ipcRenderer.invoke('plugin-settings:save', {
                pluginId,
                settings,
                correlationId
            });

            logUserAction('settings_save_success', {
                correlationId,
                result
            });

            return result;
        } catch (error) {
            logUserAction('settings_save_error', {
                correlationId,
                error: error.message
            });
            throw error;
        }
    },

    /**
     * Validate plugin settings
     * @param {Object} settings - Settings to validate
     * @returns {Promise<Object>} Validation result
     */
    async validate(settings) {
        const correlationId = generateCorrelationId();
        
        logUserAction('settings_validate_attempt', {
            settingsKeys: Object.keys(settings),
            correlationId
        });

        try {
            const result = await ipcRenderer.invoke('plugin-settings:validate', {
                pluginId,
                settings,
                correlationId
            });

            logUserAction('settings_validate_success', {
                correlationId,
                isValid: result.isValid
            });

            return result;
        } catch (error) {
            logUserAction('settings_validate_error', {
                correlationId,
                error: error.message
            });
            throw error;
        }
    },

    /**
     * Load plugin settings
     * @returns {Promise<Object>} Current settings
     */
    async load() {
        const correlationId = generateCorrelationId();
        
        logUserAction('settings_load_attempt', {
            correlationId
        });

        try {
            const result = await ipcRenderer.invoke('plugin-settings:load', {
                pluginId,
                correlationId
            });

            logUserAction('settings_load_success', {
                correlationId,
                settingsKeys: Object.keys(result || {})
            });

            return result;
        } catch (error) {
            logUserAction('settings_load_error', {
                correlationId,
                error: error.message
            });
            throw error;
        }
    }
};

/**
 * UI feedback operations
 */
const uiAPI = {
    /**
     * Show success message
     * @param {string} message - Success message
     * @param {Object} options - Display options
     */
    showSuccess(message, options = {}) {
        const correlationId = generateCorrelationId();
        
        logUserAction('ui_show_success', {
            message,
            options,
            correlationId
        });

        ipcRenderer.send('plugin-ui:show-success', {
            pluginId,
            message,
            options,
            correlationId
        });
    },

    /**
     * Show error message
     * @param {string} message - Error message
     * @param {Object} options - Display options
     */
    showError(message, options = {}) {
        const correlationId = generateCorrelationId();
        
        logUserAction('ui_show_error', {
            message,
            options,
            correlationId
        });

        ipcRenderer.send('plugin-ui:show-error', {
            pluginId,
            message,
            options,
            correlationId
        });
    },

    /**
     * Show loading state
     * @param {boolean} isLoading - Whether to show loading
     * @param {string} message - Loading message
     */
    showLoading(isLoading, message = 'Loading...') {
        const correlationId = generateCorrelationId();
        
        logUserAction('ui_show_loading', {
            isLoading,
            message,
            correlationId
        });

        ipcRenderer.send('plugin-ui:show-loading', {
            pluginId,
            isLoading,
            message,
            correlationId
        });
    },

    /**
     * Close the plugin window
     */
    closeWindow() {
        const correlationId = generateCorrelationId();
        
        logUserAction('ui_close_window', {
            correlationId
        });

        ipcRenderer.send('plugin-window:close', {
            pluginId,
            correlationId
        });
    }
};

/**
 * Logger operations for plugin UI
 */
const loggerAPI = {
    /**
     * Log user action
     * @param {string} action - Action name
     * @param {Object} context - Additional context
     */
    logUserAction(action, context = {}) {
        logUserAction(action, context);
    },

    /**
     * Log debug information
     * @param {string} message - Debug message
     * @param {Object} context - Additional context
     */
    debug(message, context = {}) {
        const correlationId = generateCorrelationId();
        ipcRenderer.send('plugin-log:debug', {
            pluginId,
            message,
            context,
            correlationId
        });
    },

    /**
     * Log info information
     * @param {string} message - Info message
     * @param {Object} context - Additional context
     */
    info(message, context = {}) {
        const correlationId = generateCorrelationId();
        ipcRenderer.send('plugin-log:info', {
            pluginId,
            message,
            context,
            correlationId
        });
    },

    /**
     * Log warning information
     * @param {string} message - Warning message
     * @param {Object} context - Additional context
     */
    warn(message, context = {}) {
        const correlationId = generateCorrelationId();
        ipcRenderer.send('plugin-log:warn', {
            pluginId,
            message,
            context,
            correlationId
        });
    },

    /**
     * Log error information
     * @param {string} message - Error message
     * @param {Object} context - Additional context
     */
    error(message, context = {}) {
        const correlationId = generateCorrelationId();
        ipcRenderer.send('plugin-log:error', {
            pluginId,
            message,
            context,
            correlationId
        });
    }
};

/**
 * Utility functions for plugin development
 */
const utilsAPI = {
    /**
     * Get plugin information
     * @returns {Object} Plugin information
     */
    getPluginInfo() {
        return {
            pluginId,
            correlationId: initialCorrelationId,
            timestamp: new Date().toISOString()
        };
    },

    /**
     * Generate a new correlation ID
     * @returns {string} New correlation ID
     */
    generateCorrelationId() {
        return generateCorrelationId();
    },

    /**
     * Get current timestamp
     * @returns {string} ISO timestamp
     */
    getTimestamp() {
        return new Date().toISOString();
    }
};

// Set up DOM event tracking for automatic user action logging
function setupDOMEventTracking() {
    document.addEventListener('DOMContentLoaded', () => {
        // Track button clicks
        document.addEventListener('click', (event) => {
            if (event.target.tagName === 'BUTTON') {
                logUserAction('button_click', {
                    buttonId: event.target.id,
                    buttonText: event.target.textContent?.trim(),
                    buttonClass: event.target.className
                });
            }
        });

        // Track form submissions
        document.addEventListener('submit', (event) => {
            const form = event.target;
            const formData = new FormData(form);
            const formFields = {};
            
            for (let [key, value] of formData.entries()) {
                // Don't log sensitive data
                if (key.toLowerCase().includes('password') || key.toLowerCase().includes('key')) {
                    formFields[key] = '[REDACTED]';
                } else {
                    formFields[key] = value;
                }
            }

            logUserAction('form_submit', {
                formId: form.id,
                formFields
            });
        });

        // Track input changes for settings forms
        document.addEventListener('change', (event) => {
            const input = event.target;
            if (input.tagName === 'INPUT' || input.tagName === 'SELECT' || input.tagName === 'TEXTAREA') {
                logUserAction('input_change', {
                    inputId: input.id,
                    inputName: input.name,
                    inputType: input.type,
                    // Don't log sensitive values
                    hasValue: input.value ? input.value.length > 0 : false
                });
            }
        });

        logUserAction('dom_ready', {
            url: window.location.href,
            title: document.title
        });
    });
}

// Initialize DOM event tracking
setupDOMEventTracking();

// Expose the plugin API to the renderer process
contextBridge.exposeInMainWorld('pluginAPI', {
    settings: settingsAPI,
    ui: uiAPI,
    logger: loggerAPI,
    utils: utilsAPI
});

// Log that the preload script has been loaded
logUserAction('preload_script_loaded', {
    pluginId,
    initialCorrelationId
});