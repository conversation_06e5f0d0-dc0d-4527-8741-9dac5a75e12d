#!/bin/bash
# Comprehensive Test Runner for Lifeboard
# Purpose: Run all test suites including infrastructure, core functionality, and edge cases
# Usage: ./tests/run_all_tests.sh

# Note: Removed set -e to allow running all tests even if some fail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
POSTGRES_PORT=${POSTGRES_PORT:-5543}
DB_HOST="localhost"
DB_NAME="postgres"
DB_USER="supabase_admin"
DB_PASSWORD=${POSTGRES_PASSWORD:-"db_ae8f36fc9b284572aa5b4c03e1ec2bce"}

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Test environment setup/teardown tracking
SETUP_FUNCTIONS=()
TEARDOWN_FUNCTIONS=()

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Lifeboard Comprehensive Test Suite${NC}"
echo -e "${BLUE}============================================${NC}"
echo "Running all test categories..."
echo

# Check and start Docker Desktop (from reset_and_start.sh)
check_and_start_docker() {
    echo -e "${YELLOW}Checking Docker daemon status...${NC}"

    # Check if Docker daemon is running
    if docker info > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Docker daemon is already running${NC}"
        return 0
    fi

    echo -e "${YELLOW}Docker daemon not running, starting Docker Desktop...${NC}"

    # Start Docker Desktop
    open -a Docker

    # Wait for Docker daemon to start
    echo -e "${YELLOW}Waiting for Docker daemon to start...${NC}"
    local max_attempts=60  # Wait up to 2 minutes
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker info > /dev/null 2>&1; then
            echo -e "${GREEN}✓ Docker daemon is now running${NC}"
            # Give it a few more seconds to fully initialize
            sleep 3
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            echo -e "${RED}ERROR: Docker daemon failed to start after $max_attempts attempts${NC}"
            echo "Please start Docker Desktop manually and try again"
            exit 1
        fi

        echo -e "${YELLOW}Attempt $attempt/$max_attempts - waiting for Docker daemon...${NC}"
        sleep 2
        ((attempt++))
    done
}

# Kill processes on occupied ports (from reset_and_start.sh)
kill_port_processes() {
    local ports=(
        5432   # PostgreSQL
        3000   # Supabase API / Next.js
        8810   # Supabase REST API
        8811   # Supabase Studio
        5543   # Custom PostgreSQL port
    )

    echo -e "${YELLOW}Killing processes on occupied ports...${NC}"

    for port in "${ports[@]}"; do
        local pids
        pids=$(lsof -ti:"$port" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            echo -e "${YELLOW}Killing processes on port $port: $pids${NC}"
            echo "$pids" | xargs kill -9 2>/dev/null || true
            echo -e "${GREEN}✓ Freed port $port${NC}"
        fi
    done
}

# Start Docker services with health checks (from reset_and_start.sh)
start_docker_services() {
    echo -e "${YELLOW}Starting Docker services...${NC}"

    cd /Users/<USER>/code/lifeboard-supabase || {
        echo -e "${RED}ERROR: Cannot change to project root directory${NC}"
        exit 1
    }

    # Start main services
    echo -e "${YELLOW}Starting main Lifeboard services...${NC}"
    docker compose -p lifeboard -f docker-compose.yml -f docker-compose.logging.yml up -d

    # Wait for database to be healthy
    echo -e "${YELLOW}Waiting for database to be ready...${NC}"
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker compose -p lifeboard ps | grep "db" | grep -q "healthy"; then
            echo -e "${GREEN}✓ Database is healthy${NC}"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            echo -e "${RED}ERROR: Database failed to become healthy after $max_attempts attempts${NC}"
            exit 1
        fi

        echo -e "${YELLOW}Attempt $attempt/$max_attempts - waiting for database...${NC}"
        sleep 2
        ((attempt++))
    done

    echo -e "${GREEN}✓ Docker services started${NC}"
}

# Verify services are healthy
verify_docker_services() {
    echo -e "${YELLOW}Verifying Docker services...${NC}"

    # Check service health
    local services=("db" "auth" "rest")
    for service in "${services[@]}"; do
        if docker compose -p lifeboard ps | grep "$service" | grep -q "healthy"; then
            echo -e "${GREEN}✓ $service is healthy${NC}"
        else
            echo -e "${YELLOW}⚠ $service is not healthy yet${NC}"
        fi
    done
}

# Global setup function - ensures all dependencies are available
setup_test_environment() {
    echo -e "${YELLOW}Setting up test environment...${NC}"

    # Check and start Docker Desktop
    check_and_start_docker

    # Kill any processes that might be blocking ports
    kill_port_processes

    # Start Docker services with proper health checks
    start_docker_services

    # Verify services are healthy
    verify_docker_services

    # Final database connection test
    local max_attempts=10
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
            echo -e "${GREEN}✓ Database connection successful${NC}"
            break
        fi
        echo -e "${YELLOW}Waiting for database connection... (attempt $attempt/$max_attempts)${NC}"
        sleep 2
        ((attempt++))
    done

    if [ $attempt -gt $max_attempts ]; then
        echo -e "${RED}ERROR: Database connection failed after $max_attempts attempts${NC}"
        exit 1
    fi

    # Create test schema for isolation
    PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        CREATE SCHEMA IF NOT EXISTS test_isolation;
        SET search_path = test_isolation, public;
    " > /dev/null 2>&1

    echo -e "${GREEN}✓ Test environment setup complete${NC}"
}

# Global teardown function - cleans up after all tests
teardown_test_environment() {
    echo -e "${YELLOW}Cleaning up test environment...${NC}"

    # Clean up test schema
    PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        DROP SCHEMA IF EXISTS test_isolation CASCADE;
    " > /dev/null 2>&1

    # Clean up any test data in public schema
    PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        -- Remove any test data (tables with test_ prefix)
        DO \$\$
        DECLARE
            r RECORD;
        BEGIN
            FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE 'test_%')
            LOOP
                EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
            END LOOP;
        END \$\$;

        -- Reset any sequences that might have been affected
        SELECT setval(pg_get_serial_sequence(schemaname||'.'||tablename, column_name), 1, false)
        FROM information_schema.columns
        WHERE column_default LIKE 'nextval%'
        AND table_schema = 'public';
    " > /dev/null 2>&1

    echo -e "${GREEN}✓ Test environment cleanup complete${NC}"
}

# Enhanced function to run SQL test file with setup/teardown
run_sql_test() {
    local test_file=$1
    local test_name=$2
    local category=$3

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}[$category] Running: ${test_name}${NC}"

    # Check if test file exists
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}✗ ERROR: Test file not found: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FILE NOT FOUND")
        return 1
    fi

    # Pre-test setup: Create isolated transaction
    local setup_sql="
        BEGIN;
        SET search_path = test_isolation, public;
        CREATE TEMP TABLE IF NOT EXISTS test_cleanup_log (
            table_name TEXT,
            action TEXT,
            timestamp TIMESTAMP DEFAULT NOW()
        );
    "

    # Create enhanced test file with setup/teardown
    local enhanced_test_file="/tmp/enhanced_${test_file##*/}"
    cat > "$enhanced_test_file" << EOF
-- Enhanced test with setup/teardown
$setup_sql

-- Original test content
$(cat "$test_file")

-- Post-test cleanup
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Log what we're cleaning up
    INSERT INTO test_cleanup_log (table_name, action)
    SELECT schemaname||'.'||tablename, 'DROP'
    FROM pg_tables
    WHERE schemaname IN ('test_isolation', 'public')
    AND tablename LIKE 'test_%';

    -- Clean up test tables
    FOR r IN (SELECT schemaname, tablename FROM pg_tables WHERE schemaname IN ('test_isolation', 'public') AND tablename LIKE 'test_%')
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END \$\$;

ROLLBACK; -- Rollback the transaction to undo any changes
EOF

    # Run the enhanced test and capture output
    if OUTPUT=$(PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$enhanced_test_file" 2>&1); then
        echo -e "${GREEN}✓ [$category] $test_name PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ [$category] $test_name - PASSED")

        # Show any notices or warnings
        echo "$OUTPUT" | grep -E "(NOTICE|WARNING):" | head -3 | while read -r line; do
            echo -e "${BLUE}   $line${NC}"
        done

        # Cleanup temp file
        rm -f "$enhanced_test_file"
        return 0
    else
        echo -e "${RED}✗ [$category] $test_name FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FAILED")

        # Show first few lines of error
        echo "$OUTPUT" | head -5 | while read -r line; do
            echo -e "${RED}   $line${NC}"
        done

        # Cleanup temp file
        rm -f "$enhanced_test_file"
        return 1
    fi
}

# Enhanced function to run shell test with environment setup/teardown
run_shell_test() {
    local test_file=$1
    local test_name=$2
    local category=$3

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}[$category] Running: ${test_name}${NC}"

    if [ ! -f "$test_file" ]; then
        echo -e "${RED}✗ ERROR: Test file not found: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FILE NOT FOUND")
        return 1
    fi

    # Create enhanced shell test wrapper
    local enhanced_test_file="/tmp/enhanced_${test_file##*/}"
    cat > "$enhanced_test_file" << 'EOF'
#!/bin/bash
# Enhanced test wrapper with setup/teardown

# Test-specific environment setup
setup_shell_test() {
    # Export database connection info
    export POSTGRES_PORT=${POSTGRES_PORT:-5543}
    export DB_HOST="localhost"
    export DB_NAME="postgres"
    export DB_USER="supabase_admin"
    export DB_PASSWORD=${POSTGRES_PASSWORD:-"db_ae8f36fc9b284572aa5b4c03e1ec2bce"}

    # Create test working directory
    export TEST_WORK_DIR="/tmp/test_work_$$"
    mkdir -p "$TEST_WORK_DIR"

    # Setup test database connection
    if ! PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "ERROR: Cannot connect to database in shell test"
        return 1
    fi
}

# Test-specific environment teardown
teardown_shell_test() {
    # Remove test working directory
    if [ -n "$TEST_WORK_DIR" ] && [ -d "$TEST_WORK_DIR" ]; then
        rm -rf "$TEST_WORK_DIR"
    fi

    # Clean up any test processes
    pkill -f "test_.*\.sh" || true

    # Clean up any test files in /tmp
    find /tmp -name "test_*" -type f -mmin +5 -delete || true
}

# Trap to ensure cleanup on exit
trap teardown_shell_test EXIT

# Run setup
setup_shell_test || exit 1

# Run the original test
EOF

    # Append the original test content
    cat "$test_file" >> "$enhanced_test_file"

    # Make it executable
    chmod +x "$enhanced_test_file"

    # Run the enhanced test
    if "$enhanced_test_file"; then
        echo -e "${GREEN}✓ [$category] $test_name PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ [$category] $test_name - PASSED")

        # Cleanup temp file
        rm -f "$enhanced_test_file"
        return 0
    else
        echo -e "${RED}✗ [$category] $test_name FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FAILED")

        # Cleanup temp file
        rm -f "$enhanced_test_file"
        return 1
    fi
}

# Enhanced function to run JavaScript/Jest tests
run_js_test() {
    local test_file=$1
    local test_name=$2
    local category=$3

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}[$category] Running: ${test_name}${NC}"

    # Check if test file exists
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}✗ ERROR: Test file not found: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FILE NOT FOUND")
        return 1
    fi

    # Change to root directory to run Jest (where jest.config.js is located)
    local original_dir=$(pwd)
    cd ../

    # Check if Node.js and npm are available
    if ! command -v node >/dev/null 2>&1; then
        echo -e "${RED}✗ ERROR: Node.js not found. Please install Node.js to run JavaScript tests.${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - NODE.JS NOT FOUND")
        cd "$original_dir"
        return 1
    fi

    if ! command -v npm >/dev/null 2>&1; then
        echo -e "${RED}✗ ERROR: npm not found. Please install npm to run JavaScript tests.${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - NPM NOT FOUND")
        cd "$original_dir"
        return 1
    fi

    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}Installing npm dependencies...${NC}"
        if ! npm install; then
            echo -e "${RED}✗ ERROR: Failed to install npm dependencies${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            TEST_RESULTS+=("❌ [$category] $test_name - NPM INSTALL FAILED")
            cd "$original_dir"
            return 1
        fi
    fi

    # Run the specific test file with Jest
    local test_path="tests/$test_file"
    echo -e "${BLUE}   Running: npx jest \"$test_path\" --verbose${NC}"
    
    if OUTPUT=$(npx jest "$test_path" --verbose --no-cache 2>&1); then
        echo -e "${GREEN}✓ [$category] $test_name PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ [$category] $test_name - PASSED")
        
        # Show test summary
        echo "$OUTPUT" | grep -E "(PASS|FAIL|Tests:|Test Suites:)" | head -5 | while read -r line; do
            echo -e "${BLUE}   $line${NC}"
        done
        
        cd "$original_dir"
        return 0
    else
        echo -e "${RED}✗ [$category] $test_name FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FAILED")
        
        # Show first few lines of error
        echo "$OUTPUT" | head -10 | while read -r line; do
            echo -e "${RED}   $line${NC}"
        done
        
        cd "$original_dir"
        return 1
    fi
}

# Setup test environment before running tests
setup_test_environment

# Trap to ensure cleanup on script exit
trap teardown_test_environment EXIT

# Phase 0: Bootstrap Validation (should run first)
echo -e "${BLUE}=== Phase 0: Bootstrap Validation ===${NC}"
run_sql_test "./test_bootstrap_validation.sql" "Bootstrap Implementation Test" "BOOTSTRAP"
echo

# Phase 1: Infrastructure Tests
echo -e "${BLUE}=== Phase 1: Infrastructure Tests ===${NC}"
run_sql_test "./test_migration_smoke.sql" "Migration Smoke Test" "INFRASTRUCTURE"
run_sql_test "./test_seed_data.sql" "Seed Data Test" "INFRASTRUCTURE"
echo

# Phase 1.5: JavaScript Unit Tests
echo -e "${BLUE}=== Phase 1.5: JavaScript Unit Tests ===${NC}"
run_js_test "SecretManager.test.js" "Secret Manager Unit Tests" "UNIT"
run_js_test "SettingsManager.test.js" "Settings Manager Unit Tests" "UNIT"
run_js_test "SettingsValidationHTML.test.js" "Settings Validation HTML Tests" "UNIT"
run_js_test "CoreLogger.test.js" "Core Logger Unit Tests" "UNIT"
run_js_test "cryptoUtilities.test.js" "Crypto Utilities Unit Tests" "UNIT"
echo

# Phase 2: Security and Isolation Tests
echo -e "${BLUE}=== Phase 2: Security & Isolation Tests ===${NC}"
run_sql_test "./test_phase2_isolation.sql" "Network Isolation Test" "SECURITY"
run_shell_test "./test_container_security.sh" "Container Security Test" "SECURITY"
if [ -x "./test_health_checks.sh" ]; then
    run_shell_test "./test_health_checks.sh" "Health Check Validation" "SECURITY"
else
    echo -e "${YELLOW}⚠ Health checks test not found or not executable${NC}"
fi
echo

# Core Functionality Tests
echo -e "${BLUE}=== Core Functionality Tests ===${NC}"
run_sql_test "./test_core_functionality.sql" "CRUD Operations & Business Logic" "CORE"
if [ -x "./test_crud_integration.sh" ]; then
    run_shell_test "./test_crud_integration.sh" "CRUD Integration Tests" "CORE"
else
    echo -e "${YELLOW}⚠ CRUD integration test not found or not executable${NC}"
fi
echo

# Edge Cases and Stress Tests
echo -e "${BLUE}=== Edge Cases & Stress Tests ===${NC}"
run_sql_test "./test_edge_cases.sql" "Boundary Conditions & Error Handling" "EDGE_CASES"
echo

# Latest Changes Validation
echo -e "${BLUE}=== Latest Changes Validation ===${NC}"
if [ -x "./test_latest_changes_validation.sh" ]; then
    run_shell_test "./test_latest_changes_validation.sh" "Latest Changes Validation" "VALIDATION"
else
    echo -e "${YELLOW}⚠ Latest changes validation test not found or not executable${NC}"
fi
echo

# Phase 5: Secrets and Configuration Validation
echo -e "${BLUE}=== Phase 5: Secrets & Configuration Validation ===${NC}"
if [ -x "./test_secrets_validation.sh" ]; then
    run_shell_test "./test_secrets_validation.sh" "Environment & Secrets Validation" "CONFIG"
else
    echo -e "${YELLOW}⚠ Secrets validation test not found or not executable${NC}"
fi
echo

# Phase 6: Observability & Logging
echo -e "${BLUE}=== Phase 6: Observability & Logging ===${NC}"
if [ -x "./test_observability_logging.sh" ]; then
    run_shell_test "./test_observability_logging.sh" "Structured Logging & Log Management" "OBSERVABILITY"
else
    echo -e "${YELLOW}⚠ Observability logging test not found or not executable${NC}"
fi
if [ -x "./test_observability_logging_plugin.sh" ]; then
    run_shell_test "./test_observability_logging_plugin.sh" "Observability Plugin Tests" "OBSERVABILITY"
else
    echo -e "${YELLOW}⚠ Observability plugin test not found or not executable${NC}"
fi
echo

# Phase 7: Security Scanning
echo -e "${BLUE}=== Phase 7: Security Scanning ===${NC}"
if [ -x "./test_security_scan.sh" ]; then
    # Security scan has different exit codes, handle them appropriately
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}[SECURITY] Running: Comprehensive Security Scan${NC}"

    if OUTPUT=$("./test_security_scan.sh" 2>&1); then
        echo -e "${GREEN}✓ [SECURITY] Security Scan COMPLETED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ [SECURITY] Security Scan - COMPLETED")
    else
        EXIT_CODE=$?
        case $EXIT_CODE in
            1)
                echo -e "${YELLOW}⚠ [SECURITY] Security Scan - HIGH PRIORITY ISSUES FOUND${NC}"
                PASSED_TESTS=$((PASSED_TESTS + 1))  # Still pass, but with warnings
                TEST_RESULTS+=("⚠️ [SECURITY] Security Scan - HIGH PRIORITY ISSUES")
                ;;
            2)
                echo -e "${RED}✗ [SECURITY] Security Scan - CRITICAL ISSUES FOUND${NC}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                TEST_RESULTS+=("❌ [SECURITY] Security Scan - CRITICAL ISSUES")
                ;;
            *)
                echo -e "${RED}✗ [SECURITY] Security Scan FAILED${NC}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                TEST_RESULTS+=("❌ [SECURITY] Security Scan - FAILED")
                ;;
        esac
    fi
else
    echo -e "${YELLOW}⚠ Security scan test not found or not executable${NC}"
fi
if [ -x "./test_security_scan_complex.sh" ]; then
    run_shell_test "./test_security_scan_complex.sh" "Complex Security Scan" "SECURITY"
else
    echo -e "${YELLOW}⚠ Complex security scan test not found or not executable${NC}"
fi
echo

# Phase 8: Web UI Health Check
echo -e "${BLUE}=== Phase 8: Web UI Health Check ===${NC}"
if [ -x "./test_webui_health.sh" ]; then
    run_shell_test "./test_webui_health.sh" "Web UI Health Check" "WEBUI"
else
    echo -e "${YELLOW}⚠ Web UI health test not found or not executable${NC}"
fi
if [ -x "./test_webui_config_validation.sh" ]; then
    run_shell_test "./test_webui_config_validation.sh" "Web UI Config Validation" "WEBUI"
else
    echo -e "${YELLOW}⚠ Web UI config validation test not found or not executable${NC}"
fi
echo

# Phase 9: Web UI Integration Test
echo -e "${BLUE}=== Phase 9: Web UI Integration Test ===${NC}"
if [ -x "./test_webui_integration.sh" ]; then
    run_shell_test "./test_webui_integration.sh" "Web UI Integration Test" "WEBUI"
else
    echo -e "${YELLOW}⚠ Web UI integration test not found or not executable${NC}"
fi
echo

# Phase 10: Plugin Architecture Test
echo -e "${BLUE}=== Phase 10: Plugin Architecture Test ===${NC}"
if [ -x "./test_plugin_loader.sh" ]; then
    run_shell_test "./test_plugin_loader.sh" "Plugin Loader & Manifest Validation" "PLUGIN_ARCHITECTURE"
else
    echo -e "${YELLOW}⚠ Plugin loader test not found or not executable${NC}"
fi
if [ -x "./test_plugin_m5.sh" ]; then
    run_shell_test "./test_plugin_m5.sh" "Plugin M5 Tests" "PLUGIN_ARCHITECTURE"
else
    echo -e "${YELLOW}⚠ Plugin M5 test not found or not executable${NC}"
fi
if [ -x "./test_plugin_m6.sh" ]; then
    run_shell_test "./test_plugin_m6.sh" "Plugin M6 Tests" "PLUGIN_ARCHITECTURE"
else
    echo -e "${YELLOW}⚠ Plugin M6 test not found or not executable${NC}"
fi
echo

# Phase 10 M4: UI Integration Tests
echo -e "${BLUE}=== Phase 10 M4: UI Integration Tests ===${NC}"
if [ -x "./test_plugin_ui_m4.sh" ]; then
    run_shell_test "./test_plugin_ui_m4.sh" "Plugin UI M4 Features" "PLUGIN_UI"
else
    echo -e "${YELLOW}⚠ Plugin UI M4 test not found or not executable${NC}"
fi

if [ -x "./test_plugin_ui_integration_m4.sh" ]; then
    run_shell_test "./test_plugin_ui_integration_m4.sh" "UI Integration & Security" "PLUGIN_UI_INTEGRATION"
else
    echo -e "${YELLOW}⚠ UI integration test not found or not executable${NC}"
fi

if [ -x "./test_ui_performance_m4.sh" ]; then
    run_shell_test "./test_ui_performance_m4.sh" "UI Performance & Memory" "PLUGIN_UI_PERFORMANCE"
else
    echo -e "${YELLOW}⚠ UI performance test not found or not executable${NC}"
fi
echo

# Performance Summary
echo -e "${BLUE}=== Performance Analysis ===${NC}"
echo -e "${YELLOW}Analyzing database performance metrics...${NC}"

# Get basic database stats
if DB_STATS=$(PGPASSWORD="$DB_PASSWORD" /opt/homebrew/opt/postgresql@14/bin/psql -h "$DB_HOST" -p "$POSTGRES_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY (n_tup_ins + n_tup_upd + n_tup_del) DESC
LIMIT 5;" 2>/dev/null); then
    echo -e "${GREEN}Top 5 Most Active Tables:${NC}"
    echo "$DB_STATS"
else
    echo -e "${YELLOW}Could not retrieve database statistics${NC}"
fi
echo

# Final Summary
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Test Results Summary${NC}"
echo -e "${BLUE}============================================${NC}"

for result in "${TEST_RESULTS[@]}"; do
    echo -e "$result"
done

echo
echo -e "${BLUE}Overall Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}✓ Infrastructure is stable${NC}"
    echo -e "${GREEN}✓ Security configuration verified${NC}"
    echo -e "${GREEN}✓ Core functionality working${NC}"
    echo -e "${GREEN}✓ Edge cases handled properly${NC}"
    echo -e "${GREEN}✓ Secrets and configuration validated${NC}"
    echo -e "${GREEN}✓ Observability and logging operational${NC}"
    echo -e "${GREEN}✓ Security scan completed${NC}"
    echo
    echo -e "${BLUE}System is ready for development!${NC}"
    exit 0
else
    PASS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    echo -e "${RED}❌ Some tests failed (${PASS_RATE}% pass rate)${NC}"
    echo -e "${YELLOW}Please review failed tests and fix issues before proceeding${NC}"
    exit 1
fi
