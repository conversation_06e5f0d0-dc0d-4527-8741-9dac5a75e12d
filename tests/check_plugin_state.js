#!/usr/bin/env node

/**
 * Plugin State Verification Script
 * 
 * This script helps verify the current state of the Limitless plugin across
 * all storage locations to ensure our default state changes are properly applied.
 * 
 * Usage: node check_plugin_state.js
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Configuration
const PLUGIN_ID = 'limitless';

/**
 * Get the platform-specific AppData directory
 */
function getAppDataDir() {
    switch (process.platform) {
        case 'win32':
            return path.join(os.homedir(), 'AppData', 'Roaming');
        case 'darwin':
            return path.join(os.homedir(), 'Library', 'Application Support');
        case 'linux':
            return path.join(os.homedir(), '.config');
        default:
            return path.join(os.homedir(), '.config');
    }
}

/**
 * Check local file system state
 */
function checkLocalFileState() {
    console.log('\n🔍 Checking Local File System State...');
    console.log('=' .repeat(50));
    
    const appDataDir = getAppDataDir();
    const pluginStateDir = path.join(appDataDir, 'lifeboard', 'plugins', PLUGIN_ID);
    const stateFilePath = path.join(pluginStateDir, 'state.json');
    const settingsFilePath = path.join(pluginStateDir, 'settings.json');
    
    console.log(`📁 Plugin directory: ${pluginStateDir}`);
    console.log(`📄 State file: ${stateFilePath}`);
    console.log(`⚙️  Settings file: ${settingsFilePath}`);
    
    // Check if directory exists
    if (!fs.existsSync(pluginStateDir)) {
        console.log('❌ Plugin directory does not exist');
        console.log('✅ This is expected for a fresh installation - plugin will be disabled by default');
        return { exists: false, state: 'disabled (default)' };
    }
    
    // Check state file
    if (fs.existsSync(stateFilePath)) {
        try {
            const stateData = JSON.parse(fs.readFileSync(stateFilePath, 'utf8'));
            console.log('📊 State file contents:');
            console.log(JSON.stringify(stateData, null, 2));
            return { exists: true, state: stateData.state, data: stateData };
        } catch (error) {
            console.log(`❌ Error reading state file: ${error.message}`);
            return { exists: true, state: 'error', error: error.message };
        }
    } else {
        console.log('❌ State file does not exist');
        console.log('✅ This is expected for a fresh installation - plugin will be disabled by default');
        return { exists: false, state: 'disabled (default)' };
    }
}

/**
 * Check global plugin registry
 */
function checkGlobalRegistry() {
    console.log('\n🔍 Checking Global Plugin Registry...');
    console.log('=' .repeat(50));
    
    const appDataDir = getAppDataDir();
    const registryPath = path.join(appDataDir, 'lifeboard', 'plugins', 'global', 'registry.json');
    
    console.log(`📄 Registry file: ${registryPath}`);
    
    if (!fs.existsSync(registryPath)) {
        console.log('❌ Global registry file does not exist');
        console.log('✅ This is expected for a fresh installation');
        return { exists: false };
    }
    
    try {
        const registryData = JSON.parse(fs.readFileSync(registryPath, 'utf8'));
        console.log('📊 Registry file contents:');
        
        if (registryData.plugins && registryData.plugins[PLUGIN_ID]) {
            console.log(`🔌 ${PLUGIN_ID} plugin entry:`);
            console.log(JSON.stringify(registryData.plugins[PLUGIN_ID], null, 2));
            return { exists: true, plugin: registryData.plugins[PLUGIN_ID] };
        } else {
            console.log(`❌ ${PLUGIN_ID} plugin not found in registry`);
            return { exists: true, plugin: null };
        }
    } catch (error) {
        console.log(`❌ Error reading registry file: ${error.message}`);
        return { exists: true, error: error.message };
    }
}

/**
 * Check database state
 */
async function checkDatabaseState() {
    console.log('\n🔍 Checking Database State...');
    console.log('=' .repeat(50));

    console.log('ℹ️  Database check requires @supabase/supabase-js package');
    console.log('ℹ️  To check database state manually, use the provided SQL queries in query_plugin_database.sql');
    console.log('ℹ️  Or run: psql -h localhost -p 5432 -U postgres -d lifeboard -f query_plugin_database.sql');

    return { success: false, skipped: true, message: 'Database check skipped - requires Supabase client' };
}

/**
 * Check web UI state
 */
function checkWebUIState() {
    console.log('\n🔍 Checking Web UI State...');
    console.log('=' .repeat(50));
    
    const webUIPath = path.join(__dirname, 'webui', 'plugins.html');
    console.log(`📄 Web UI file: ${webUIPath}`);
    
    if (!fs.existsSync(webUIPath)) {
        console.log('❌ Web UI file does not exist');
        return { exists: false };
    }
    
    try {
        const content = fs.readFileSync(webUIPath, 'utf8');
        
        // Check plugin card state
        const cardMatch = content.match(/data-plugin="limitless"\s+data-state="([^"]+)"/);
        const cardState = cardMatch ? cardMatch[1] : 'not found';
        
        // Check checkbox state
        const checkboxChecked = content.includes('input type="checkbox" checked') && 
                               content.includes('onchange="togglePlugin(\'limitless\'');
        
        // Check JavaScript state
        const jsStateMatch = content.match(/limitless:\s*{\s*enabled:\s*(true|false)/);
        const jsState = jsStateMatch ? jsStateMatch[1] === 'true' : 'not found';
        
        console.log(`🎯 Plugin card data-state: "${cardState}"`);
        console.log(`☑️  Checkbox checked: ${checkboxChecked}`);
        console.log(`📜 JavaScript enabled state: ${jsState}`);
        
        const isCorrect = cardState === 'disabled' && !checkboxChecked && jsState === false;
        console.log(`\n${isCorrect ? '✅' : '❌'} Web UI state is ${isCorrect ? 'CORRECT' : 'INCORRECT'}`);
        
        return {
            exists: true,
            cardState,
            checkboxChecked,
            jsState,
            isCorrect
        };
    } catch (error) {
        console.log(`❌ Error reading web UI file: ${error.message}`);
        return { exists: true, error: error.message };
    }
}

/**
 * Main verification function
 */
async function main() {
    console.log('🔍 Limitless Plugin State Verification');
    console.log('=' .repeat(50));
    console.log(`Plugin ID: ${PLUGIN_ID}`);
    console.log(`Platform: ${process.platform}`);
    console.log(`Node.js: ${process.version}`);
    
    // Check all storage locations
    const localState = checkLocalFileState();
    const registryState = checkGlobalRegistry();
    const webUIState = checkWebUIState();
    const dbState = await checkDatabaseState();
    
    // Summary
    console.log('\n📋 SUMMARY');
    console.log('=' .repeat(50));
    console.log(`Local File State: ${localState.state || 'N/A'}`);
    console.log(`Global Registry: ${registryState.exists ? (registryState.plugin ? registryState.plugin.state : 'Not registered') : 'N/A'}`);
    console.log(`Web UI State: ${webUIState.exists ? (webUIState.isCorrect ? 'Disabled (Correct)' : 'Enabled (Incorrect)') : 'N/A'}`);
    console.log(`Database State: ${dbState.skipped ? 'Skipped' : (dbState.success ? (dbState.plugins?.length > 0 ? `${dbState.plugins.length} record(s)` : 'No records') : 'Error')}`);

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS');
    console.log('=' .repeat(50));

    if (webUIState.exists && webUIState.isCorrect) {
        console.log('✅ Web UI changes have been successfully applied');
        console.log('✅ New users will see the Limitless plugin as disabled by default');
    } else if (webUIState.exists && !webUIState.isCorrect) {
        console.log('❌ Web UI still shows plugin as enabled by default');
        console.log('🔧 Please verify the changes in webui/plugins.html');
    }

    if (!localState.exists && (dbState.skipped || !dbState.success || !dbState.plugins || dbState.plugins.length === 0)) {
        console.log('✅ Fresh installation state is correct - plugin will be disabled by default');
    }
    
    console.log('\n🎯 To test the changes:');
    console.log('1. Clear any existing plugin state files');
    console.log('2. Open the plugins page in a fresh browser session');
    console.log('3. Verify the Limitless plugin slider is in the "off" position');
}

// Run the verification
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    checkLocalFileState,
    checkGlobalRegistry,
    checkDatabaseState,
    checkWebUIState
};
