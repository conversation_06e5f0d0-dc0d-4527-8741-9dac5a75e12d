module.exports = {
    // Override test environment for HTML tests
    projects: [
        {
            displayName: 'node',
            testEnvironment: 'node',
            testMatch: ['<rootDir>/tests/**/!(SettingsValidationHTML).test.js'],
            testPathIgnorePatterns: ['/node_modules/'],
            clearMocks: true,
            resetMocks: true,
            restoreMocks: true,
            coverageDirectory: 'coverage',
            collectCoverageFrom: [
                'desktop/**/*.js',
                '!desktop/node_modules/**',
                '!desktop/dist/**',
                '!desktop/build/**'
            ],
            moduleNameMapper: {
                '^@/(.*)$': '<rootDir>/desktop/$1'
            },
            setupFilesAfterEnv: [],
            testTimeout: 10000
        },
        {
            displayName: 'jsdom',
            testEnvironment: 'jsdom',
            testMatch: ['<rootDir>/tests/**/SettingsValidationHTML.test.js'],
            testPathIgnorePatterns: ['/node_modules/'],
            clearMocks: true,
            resetMocks: true,
            restoreMocks: true,
            coverageDirectory: 'coverage',
            collectCoverageFrom: [
                'desktop/**/*.js',
                '!desktop/node_modules/**',
                '!desktop/dist/**',
                '!desktop/build/**'
            ],
            moduleNameMapper: {
                '^@/(.*)$': '<rootDir>/desktop/$1'
            },
            setupFilesAfterEnv: [],
            testTimeout: 15000
        }
    ]
};
