volumes/db/data
volumes/storage
.env
.env.local
test.http
docker-compose.override.yml
# Logs - ignore log files but keep directory structure
logs/*.log
logs/*/*.log
logs/*/*/*.log
# Ignore any directory named logs anywhere in the project
logs/
**/logs/

# macOS
**/.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
**/.DS_Store
*/.DS_Store

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Desktop app
desktop/node_modules
desktop/dist
desktop/*.log
.augment/
interrupted/
node_modules/